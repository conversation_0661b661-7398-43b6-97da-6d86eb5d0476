const express = require("express");
const validate = require("../middlewares/validate");
const IdentityValidation = require("../validations/identity.validation");
const IdentityController = require("../controllers/identity.controller");
const auth = require("../middlewares/auth");

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Identity
 *   description: Identity, Card, and IdentityAccess management
 */

// Identity routes
/**
 * @swagger
 * /identity:
 *   post:
 *     summary: Create a new identity
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               identity_type:
 *                 type: string
 *               national_id:
 *                 type: string
 *               mobile:
 *                 type: string
 *               start_date:
 *                 type: string
 *                 format: date
 *               end_date:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: integer
 *               suspension:
 *                 type: boolean
 *               suspension_date:
 *                 type: string
 *                 format: date
 *               reason:
 *                 type: string
 *               image:
 *                 type: string
 *     responses:
 *       201:
 *         description: Identity created successfully
 *       400:
 *         description: Invalid input
 */
router.post(
  "/",
  auth("manage_identity"),
  validate(IdentityValidation.createIdentity),
  IdentityController.createIdentity
);

/**
 * @swagger
 * /identity/organization:
 *   patch:
 *     summary: Update organization details
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               identity_id:
 *                 type: string
 *                 format: uuid
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               eid:
 *                 type: string
 *               company:
 *                 type: string
 *               company_code:
 *                 type: string
 *               organization:
 *                 type: string
 *               job_title:
 *                 type: string
 *               job_code:
 *                 type: string
 *               manager:
 *                 type: string
 *               status:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Organization updated successfully
 *       404:
 *         description: Organization not found
 */
router.patch(
  "/organization",
  auth("manage_identity"),
  validate(IdentityValidation.updateOrganization),
  IdentityController.updateOrganization
);

/**
 * @swagger
 * /identity/facility:
 *   patch:
 *     summary: Update facility_id for an identity
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               facility_id:
 *                 type: string
 *                 format: uuid
 *               identity_id:
 *                 type: string
 *                 format: uuid
 *                 description: Identity ID to update with this facility_id
 *     responses:
 *       200:
 *         description: facility_id updated for identity
 *       404:
 *         description: Identity not found
 */
router.patch(
  "/facility",
  // auth("manage_facility"),
  validate(IdentityValidation.updateFacility),
  IdentityController.updateFacility
);

/**
 * @swagger
 * /identity/basic/{identity_id}: 
 *   get:
 *     summary: Get an identity by ID
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: identity_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the identity
 *     responses:
 *       200:
 *         description: Identity retrieved successfully
 *       404:
 *         description: Identity not found
 */
router.get(
  "/basic/:identity_id",
  auth("view_identity"),
  validate(IdentityValidation.getIdentity),
  IdentityController.basicIdentity
);

/**
 * @swagger
 * /identity/{identity_id}:
 *   patch:
 *     summary: Update an identity
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: identity_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the identity
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *               last_name:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               identity_type:
 *                 type: string
 *               national_id:
 *                 type: string
 *               mobile:
 *                 type: string
 *               start_date:
 *                 type: string
 *                 format: date
 *               end_date:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: integer
 *               suspension:
 *                 type: boolean
 *               suspension_date:
 *                 type: string
 *                 format: date
 *               reason:
 *                 type: string
 *               image:
 *                 type: string
 *     responses:
 *       200:
 *         description: Identity updated successfully
 *       404:
 *         description: Identity not found
 */
router.patch(
  "/:identity_id",
  auth("manage_identity"),
  validate(IdentityValidation.updateIdentity),
  IdentityController.updateIdentity
);

/**
 * @swagger
 * /identity/{identity_id}:
 *   delete:
 *     summary: Delete an identity
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: identity_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the identity
 *     responses:
 *       204:
 *         description: Identity deleted successfully
 *       404:
 *         description: Identity not found
 */
router.delete(
  "/identity/:identity_id",
  auth("manage_identity"),
  validate(IdentityValidation.deleteIdentity),
  IdentityController.deleteIdentity
);

/**
 * @swagger
 * /identity/hub:
 *   get:
 *     summary: Get identity hub data
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is DESC)
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *         description: Filter by status
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: Filter by name
 *       - in: query
 *         name: eid
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by EID
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by identity type
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by first name, last name, eid, job title, company, or organization
 *     responses:
 *       200:
 *         description: Identity hub data retrieved successfully
 *       404:
 *         description: No data found
 */
router.get(
  "/hub",
  auth("view_identity"),
  validate(IdentityValidation.getIdentityHub),
  IdentityController.getIdentityHub
);

/**
 * @swagger
 * /identity/access-list:
 *   get:
 *     summary: Get identity access list
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is DESC)
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *         description: Filter by status
 *       - in: query
 *         name: area_name
 *         schema:
 *           type: string
 *         description: Filter by area name
 *       - in: query
 *         name: eid
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by EID
 *       - in: query
 *         name: pacs_area_name
 *         schema:
 *           type: string
 *         description: Filter by PACS area name
 *       - in: query
 *         name: system
 *         schema:
 *           type: string
 *         description: Filter by system
 *     responses:
 *       200:
 *         description: Identity access list retrieved successfully
 *       404:
 *         description: No data found
 */
router.get(
  "/access-list",
  auth("view_identity_access"),
  validate(IdentityValidation.getIdentityAccessList),
  IdentityController.getIdentityAccessList
);

/**
 * @swagger
 * /identity/organization:
 *   get:
 *     summary: Get organization details
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: identity_id
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by identity ID
 *     responses:
 *       200:
 *         description: Organization details retrieved successfully
 *       404:
 *         description: No data found
 */
router.get(
  "/organization",
  auth("view_identity"),
  validate(IdentityValidation.getOrganization),
  IdentityController.getOrganization
);

// Card routes
/**
 * @swagger
 * /identity/card:
 *   post:
 *     summary: Create a new card
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               card_number:
 *                 type: string
 *               card_format:
 *                 type: integer
 *               identity_id:
 *                 type: string
 *               facility_code:
 *                 type: integer
 *               pin:
 *                 type: integer
 *               template:
 *                 type: integer
 *               active_date:
 *                 type: string
 *                 format: date
 *               deactive_date:
 *                 type: string
 *                 format: date
 *               reason:
 *                 type: string
 *               status:
 *                 type: integer
 *     responses:
 *       201:
 *         description: Card created successfully
 *       400:
 *         description: Invalid input
 */
router.post(
  "/card",
  auth("manage_card"),
  validate(IdentityValidation.createCard),
  IdentityController.createCard
);

/**
 * @swagger
 * /identity/card/{card_id}:
 *   get:
 *     summary: Get a card by ID
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: card_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the card
 *     responses:
 *       200:
 *         description: Card retrieved successfully
 *       404:
 *         description: Card not found
 */
router.get(
  "/card/:card_id",
  auth("view_card"),
  validate(IdentityValidation.getCardById),
  IdentityController.getCardbyId
);

/**
 * @swagger
 * /identity/cards:
 *   get:
 *     summary: Get all cards
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is DESC)
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *         description: Filter by status
 *       - in: query
 *         name: card_number
 *         schema:
 *           type: string
 *         description: Filter by card number
 *       - in: query
 *         name: card_format
 *         schema:
 *           type: string
 *         description: Filter by card format
 *     responses:
 *       200:
 *         description: Cards retrieved successfully
 *       404:
 *         description: No cards found
 */
router.get(
  "/cards",
  auth("view_card"),
  validate(IdentityValidation.getCards),
  IdentityController.getCards
);

/**
 * @swagger
 * /identity/card-list:
 *   get:
 *     summary: Get a list of cards
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is DESC)
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *         description: Filter by status
 *       - in: query
 *         name: card_number
 *         schema:
 *           type: string
 *         description: Filter by card number
 *       - in: query
 *         name: card_format
 *         schema:
 *           type: string
 *         description: Filter by card format
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by card number, template, or status
 *     responses:
 *       200:
 *         description: Cards retrieved successfully
 *       404:
 *         description: No cards found
 */
router.get(
  "/card-list",
  auth("view_card"),
  validate(IdentityValidation.getCardList),
  IdentityController.getCardList
);

/**
 * @swagger
 * /identity/card/{card_id}:
 *   patch:
 *     summary: Update a card
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: card_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the card
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               card_number:
 *                 type: string
 *               card_format:
 *                 type: integer
 *               facility_code:
 *                 type: integer
 *               pin:
 *                 type: integer
 *               template:
 *                 type: integer
 *               active_date:
 *                 type: string
 *                 format: date
 *               deactive_date:
 *                 type: string
 *                 format: date
 *               reason:
 *                 type: string
 *               status:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Card updated successfully
 *       404:
 *         description: Card not found
 */
router.patch(
  "/card/:card_id",
  auth("manage_card"),
  validate(IdentityValidation.updateCard),
  IdentityController.updateCard
);

/**
 * @swagger
 * /identity/card/{card_id}:
 *   delete:
 *     summary: Delete a card
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: card_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the card
 *     responses:
 *       204:
 *         description: Card deleted successfully
 *       404:
 *         description: Card not found
 */
router.delete(
  "/card/:card_id",
  auth("manage_card"),
  validate(IdentityValidation.deleteCard),
  IdentityController.deleteCard
);

// IdentityAccess routes
/**
 * @swagger
 * /identity/identity-access:
 *   post:
 *     summary: Create a new identity access
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               access_level_id:
 *                 type: uuid
 *               card_id:
 *                 type: uuid
 *               identity_id:
 *                 type: uuid
 *               start_date:
 *                 type: string
 *                 format: date
 *               end_date:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: integer
 *     responses:
 *       201:
 *         description: IdentityAccess created successfully
 *       400:
 *         description: Invalid input
 */
router.post(
  "/identity-access",
  auth("manage_identity_access"),
  validate(IdentityValidation.createIdentityAccess),
  IdentityController.createIdentityAccess
);

/**
 * @swagger
 * /identity/access-level-names:
 *   get:
 *     summary: Get all access level names
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search access level names (case-insensitive, partial match)
 *     responses:
 *       200:
 *         description: Access level names retrieved successfully
 *       404:
 *         description: No access levels found
 */
router.get(
  "/access-level-names",
  // auth("view_access_level"),
  validate(IdentityValidation.getAccessLevelName),
  IdentityController.getAccessLevelName
);

/**
 * @swagger
 * /identity/card-numbers:
 *   get:
 *     summary: Get all card numbers
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Card numbers retrieved successfully
 *       404:
 *         description: No card numbers found
 */
router.get(
  "/card-numbers",
  auth("view_card"),
  IdentityController.getCardNumber
);

/**
 * @swagger
 * /identity/identity-access/{identity_access_id}:
 *   get:
 *     summary: Get an identity access by ID
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: identity_access_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the identity access
 *     responses:
 *       200:
 *         description: IdentityAccess retrieved successfully
 *       404:
 *         description: IdentityAccess not found
 */
router.get(
  "/identity-access/:identity_access_id",
  auth("view_identity_access"),
  validate(IdentityValidation.getIdentityAccess),
  IdentityController.getIdentityAccessById
);

/**
 * @swagger
 * /identity/identity-access:
 *   get:
 *     summary: Get all identity accesses
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is DESC)
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *         description: Filter by status
 *       - in: query
 *         name: area_name
 *         schema:
 *           type: string
 *         description: Filter by area name
 *       - in: query
 *         name: eid
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by EID
 *       - in: query
 *         name: pacs_area_name
 *         schema:
 *           type: string
 *         description: Filter by PACS area name
 *       - in: query
 *         name: system
 *         schema:
 *           type: string
 *         description: Filter by system
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by access area, PACS area name, EID, or system
 *     responses:
 *       200:
 *         description: Identity accesses retrieved successfully
 *       404:
 *         description: No identity accesses found
 */
router.get(
  "/identity-access",
  auth("view_identity_access"),
  validate(IdentityValidation.getIdentityAccess),
  IdentityController.getIdentityAccess
);

/**
 * @swagger
 * /identity/identity-access/{identity_access_id}:
 *   patch:
 *     summary: Update an identity access
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: identity_access_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the identity access
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               access_level_id:
 *                 type: string
 *               card_id:
 *                 type: string
 *               start_date:
 *                 type: string
 *                 format: date
 *               end_date:
 *                 type: string
 *                 format: date
 *               status:
 *                 type: integer
 *     responses:
 *       200:
 *         description: IdentityAccess updated successfully
 *       404:
 *         description: IdentityAccess not found
 */
router.patch(
  "/identity-access/:identity_access_id",
  auth("manage_identity_access"),
  validate(IdentityValidation.updateIdentityAccess),
  IdentityController.updateIdentityAccess
);

/**
 * @swagger
 * /identity/identity-access/{identity_access_id}:
 *   delete:
 *     summary: Delete an identity access
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: identity_access_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the identity access
 *     responses:
 *       204:
 *         description: IdentityAccess deleted successfully
 *       404:
 *         description: IdentityAccess not found
 */
router.delete(
  "/identity-access/:identity_access_id",
  auth("manage_identity_access"),
  validate(IdentityValidation.deleteIdentityAccess),
  IdentityController.deleteIdentityAccess
);

/**
 * @swagger
 * /identity/identity-access-details:
 *   get:
 *     summary: Get identity access details with access level name and card number
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by (default is start_date)
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is DESC)
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *         description: Filter by status
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by access level name, card number, or status
 *     responses:
 *       200:
 *         description: Identity Access details retrieved successfully
 *       404:
 *         description: No Identity Access records found
 */
router.get(
  "/identity-access-details",
  // auth("view_identity_access"),
  validate(IdentityValidation.getIdentityAccessDetails),
  IdentityController.getIdentityAccessDetails
);

/**
 * @swagger
 * /identity/identity-access/access/{identity_id}:
 *   get:
 *     summary: Get identity access by identity ID (with access level name)
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: identity_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The identity ID
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by access level name, card number, or status
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by (default is start_date)
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is DESC)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *     responses:
 *       200:
 *         description: IdentityAccess retrieved successfully
 *       404:
 *         description: IdentityAccess not found
 */
router.get(
  "/identity-access/access/:identity_id",
  auth("view_identity_access"),
  validate(IdentityValidation.getIdentityAccessByIdentity),
  IdentityController.getIdentityAccessByIdentity
);

/**
 * @swagger
 * /identity/card/access/{identity_id}:
 *   get:
 *     summary: Get card by identity ID
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: identity_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The identity ID
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search by card number, status, or template
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by (default is card_number)
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is ASC)
 *     responses:
 *       200:
 *         description: Card retrieved successfully
 *       404:
 *         description: Card not found
 */
router.get(
  "/card/access/:identity_id",
  auth("view_card"),
  validate(IdentityValidation.getIdentityById),
  IdentityController.getCardbyIdentity
);

/**
 * @swagger
 * /identity/{identity_id}:
 *   get:
 *     summary: Get an identity by ID
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: identity_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: The ID of the identity
 *     responses:
 *       200:
 *         description: Identity retrieved successfully
 *       404:
 *         description: Identity not found
 */
router.get(
  "/:identity_id",
  auth("view_identity"),
  validate(IdentityValidation.getIdentityById),
  IdentityController.getIdentityById
);

/**
 * @swagger
 * /identity:
 *   get:
 *     summary: Get all identities
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is DESC)
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *         description: Filter by status
 *       - in: query
 *         name: name
 *         schema:
 *           type: string
 *         description: Filter by name
 *       - in: query
 *         name: eid
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by EID
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter by identity type
 *     responses:
 *       200:
 *         description: Identities retrieved successfully
 *       404:
 *         description: No identities found
 */
router.get(
  "/",
  auth("view_identity"),
  validate(IdentityValidation.getIdentity),
  IdentityController.getIdentity
);

/**
 * @swagger
 * /identity/eids:
 *   get:
 *     summary: Get all identity EIDs
 *     tags: [Identity]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Identity EIDs retrieved successfully
 *       404:
 *         description: No identities found
 */
router.get(
  "/eids",
  auth("view_identity"),
  IdentityController.getIdentityEid
);

module.exports = router;
