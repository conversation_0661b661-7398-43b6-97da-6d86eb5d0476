const Joi = require("joi");
const { unique, exists } = require("./custom.validation");

const languageId = Joi.string().required().external(exists("Language", "language_id"));

const create = {
  body: Joi.object().keys({
    name: Joi.string().required().external(unique("Language", "name")),
    code: Joi.string().required().external(unique("Language", "code")),
    default: Joi.boolean().optional(),
    status: Joi.boolean().optional(),
    addons: Joi.object().optional(), // Validate addons as a JSON object
  }),
};

const update = {
  params: Joi.object().keys({
    languageId,
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().optional(),
      code: Joi.string().optional(),
      default: Joi.boolean().optional(),
      status: Joi.boolean().optional(),
      addons: Joi.object().optional(), // Validate addons as a JSON object
    })
    .min(1),
};

const status = {
  params: Joi.object().keys({
    languageId,
  }),
  body: Joi.object().keys({
    status: Joi.boolean().required(),
  }),
};

module.exports = {
  create,
  update,
  status,
};
