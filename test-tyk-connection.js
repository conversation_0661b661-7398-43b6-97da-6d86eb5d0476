const axios = require('axios');
const config = require('./config/config');
const tykConfig = require('./config/tyk');
const logger = require('./config/logger');

/**
 * Test script to verify Tyk Gateway connectivity and configuration
 */

async function testTykConnection() {
  console.log('=== Tyk Gateway Connection Test ===\n');
  
  // Test 1: Basic Gateway Health Check
  console.log('1. Testing Tyk Gateway health endpoint...');
  try {
    const response = await axios.get(`${tykConfig.gatewayUrl}/hello`, {
      timeout: 5000
    });
    console.log('✅ Tyk Gateway is running');
    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${JSON.stringify(response.data)}\n`);
  } catch (error) {
    console.log('❌ Tyk Gateway health check failed');
    console.log(`   Error: ${error.message}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Response: ${JSON.stringify(error.response.data)}`);
    }
    console.log('');
  }

  // Test 2: Test Gateway API with Authorization
  console.log('2. Testing Tyk Gateway API access...');
  try {
    const response = await axios.get(`${tykConfig.gatewayUrl}/tyk/apis`, {
      headers: tykConfig.getGatewayHeaders(),
      timeout: 5000
    });
    console.log('✅ Tyk Gateway API access successful');
    console.log(`   Status: ${response.status}`);
    console.log(`   APIs found: ${response.data ? response.data.length : 0}\n`);
  } catch (error) {
    console.log('❌ Tyk Gateway API access failed');
    console.log(`   Error: ${error.message}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Response: ${JSON.stringify(error.response.data)}`);
    }
    console.log('');
  }

  // Test 3: Test Analytics Endpoint
  console.log('3. Testing Tyk Analytics endpoint...');
  try {
    const testAnalytics = {
      timestamp: new Date().toISOString(),
      method: 'TEST',
      path: '/test',
      raw_path: '/test',
      content_length: 0,
      user_agent: 'test-script',
      day: new Date().getDate(),
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      hour: new Date().getHours(),
      response_code: 200,
      api_key: 'test-key',
      api_version: '1.0',
      api_name: tykConfig.apiId,
      api_id: tykConfig.apiId,
      org_id: tykConfig.orgId,
      oauth_id: '',
      request_time: 100,
      raw_request: '',
      raw_response: '',
      ip_address: '127.0.0.1'
    };

    const response = await axios.post(
      `${tykConfig.gatewayUrl}/tyk/analytics/record`,
      testAnalytics,
      {
        headers: tykConfig.getGatewayHeaders(),
        timeout: 5000
      }
    );
    console.log('✅ Tyk Analytics endpoint accessible');
    console.log(`   Status: ${response.status}\n`);
  } catch (error) {
    console.log('❌ Tyk Analytics endpoint failed');
    console.log(`   Error: ${error.message}`);
    if (error.response) {
      console.log(`   Status: ${error.response.status}`);
      console.log(`   Response: ${JSON.stringify(error.response.data)}`);
    }
    console.log('');
  }

  // Test 4: Configuration Check
  console.log('4. Checking Tyk configuration...');
  console.log(`   Gateway URL: ${tykConfig.gatewayUrl}`);
  console.log(`   API ID: ${tykConfig.apiId}`);
  console.log(`   Org ID: ${tykConfig.orgId}`);
  console.log(`   Secret: ${tykConfig.secret ? '***configured***' : 'NOT SET'}`);
  console.log(`   Auth Mode: ${config.auth.mode}`);
  console.log(`   Tyk Enabled: ${tykConfig.isEnabled}\n`);

  console.log('=== Test Complete ===');
}

// Run the test
testTykConnection().catch(console.error);
