const Joi = require("joi");
const { exists, unique } = require("./custom.validation");

const countryId = Joi.string().required().external(exists("Country", "country_id"));

const CountryValidation = {
  create: {
    body: Joi.object().keys({
      name: Joi.string().required().external(unique("Country", "name")),
    }),
  },
  country: {
    params: Joi.object().keys({
      countryId,
    }),
  },
  update: {
    params: Joi.object().keys({
      countryId,
    }),
    body: Joi.object().keys({
      name: Joi.string().optional().external(unique("Country", "name")),
    }),
  },
};

module.exports = CountryValidation;