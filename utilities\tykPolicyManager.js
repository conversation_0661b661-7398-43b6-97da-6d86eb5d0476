const tykService = require('../services/tyk.service');
const tykConfig = require('../config/tyk');
const logger = require('../config/logger');
const { Role, Permission, RolePermission, Identity, IdentityRole } = require('../models');

/**
 * Tyk Policy Manager
 * Manages dynamic creation and updates of Tyk policies based on roles and permissions
 */

const tykPolicyManager = {
  policyCache: new Map(), // Cache for policy mappings
  isEnabled: tykConfig.isEnabled,

  /**
   * Cache role policy information
   */
  cacheRolePolicy(role, permissions = []) {
    if (!this.isEnabled) {
      logger.debug('Tyk not enabled, skipping policy caching');
      return null;
    }

    const policyName = `role_${role.name}_${role.role_id}`;
    const permissionNames = permissions.map(p => p.name || p);

    // Cache the policy mapping
    this.policyCache.set(role.role_id, {
      policyId: `policy_${role.role_id}`,
      policyName: policyName,
      permissions: permissionNames,
      rateLimit: this.getRateLimitForRole(role.name),
      quota: this.getQuotaForRole(role.name),
      lastUpdated: new Date()
    });

    logger.info(`Policy cached for role: ${role.name} (${role.role_id})`);
    return this.policyCache.get(role.role_id);
  },

  /**
   * Update cached policy for a role
   */
  updateRolePolicy(role, newPermissions) {
    if (!this.isEnabled) {
      logger.debug('Tyk not enabled, skipping policy update');
      return null;
    }

    const permissionNames = newPermissions.map(p => p.name || p);
    return this.cacheRolePolicy(role, permissionNames);
  },

  /**
   * Remove policy from cache
   */
  deleteRolePolicy(roleId) {
    if (!this.isEnabled) {
      logger.debug('Tyk not enabled, skipping policy deletion');
      return null;
    }

    this.policyCache.delete(roleId);
    logger.info(`Policy removed from cache for role ID: ${roleId}`);
    return true;
  },

  /**
   * Sync all roles to policy cache
   */
  async syncAllRolesToTyk() {
    if (!this.isEnabled) {
      logger.debug('Tyk not enabled, skipping role sync');
      return;
    }

    try {
      logger.info('Starting sync of all roles to policy cache...');

      // Get all active roles
      const roles = await Role.findAll({
        where: { is_active: true },
        include: [
          {
            model: Permission,
            as: 'permission',
            through: { attributes: [] }
          }
        ]
      });

      const syncResults = [];

      for (const role of roles) {
        try {
          const permissions = role.permission || [];
          const result = this.cacheRolePolicy(role, permissions);
          syncResults.push({
            roleId: role.role_id,
            roleName: role.name,
            success: !!result,
            policyId: result?.policyId
          });
        } catch (error) {
          syncResults.push({
            roleId: role.role_id,
            roleName: role.name,
            success: false,
            error: error.message
          });
        }
      }

      const successCount = syncResults.filter(r => r.success).length;
      const failureCount = syncResults.filter(r => !r.success).length;

      logger.info(`Role sync completed: ${successCount} successful, ${failureCount} failed`);

      if (failureCount > 0) {
        logger.warn('Failed role syncs:', syncResults.filter(r => !r.success));
      }

      return syncResults;

    } catch (error) {
      logger.error('Failed to sync roles to Tyk:', error.message);
      throw error;
    }
  },

  /**
   * Get permissions for a role
   */
  async getRolePermissions(roleId) {
    try {
      const rolePermissions = await RolePermission.findAll({
        where: { role_id: roleId },
        include: [
          {
            model: Permission,
            as: 'permission',
            attributes: ['permission_id', 'name', 'description']
          }
        ]
      });

      return rolePermissions.map(rp => rp.permission);
    } catch (error) {
      logger.error(`Failed to get permissions for role ${roleId}:`, error.message);
      return [];
    }
  },

  /**
   * Get roles for an identity
   */
  async getIdentityRoles(identityId) {
    try {
      const identityRoles = await IdentityRole.findAll({
        where: { identity_id: identityId },
        include: [
          {
            model: Role,
            as: 'role',
            attributes: ['role_id', 'name', 'description']
          }
        ]
      });

      return identityRoles.map(ir => ir.role);
    } catch (error) {
      logger.error(`Failed to get roles for identity ${identityId}:`, error.message);
      return [];
    }
  },

  /**
   * Get rate limit configuration for a role
   */
  getRateLimitForRole(roleName) {
    const rateLimits = {
      'admin': { rate: 10000, per: 60 },
      'kiosk': { rate: 1000, per: 60 },
      'user': { rate: 5000, per: 60 },
      'guest': { rate: 100, per: 60 }
    };

    return rateLimits[roleName.toLowerCase()] || { rate: 1000, per: 60 };
  },

  /**
   * Get quota configuration for a role
   */
  getQuotaForRole(roleName) {
    const quotas = {
      'admin': { max: -1, renews: 0, remaining: -1, renewalRate: 0 },
      'kiosk': { max: 100000, renews: Date.now() + 86400000, remaining: 100000, renewalRate: 86400 },
      'user': { max: 50000, renews: Date.now() + 86400000, remaining: 50000, renewalRate: 86400 },
      'guest': { max: 1000, renews: Date.now() + 86400000, remaining: 1000, renewalRate: 86400 }
    };

    return quotas[roleName.toLowerCase()] || { max: 10000, renews: Date.now() + 86400000, remaining: 10000, renewalRate: 86400 };
  },

  /**
   * Clear policy cache
   */
  clearCache() {
    this.policyCache.clear();
    logger.info('Tyk policy cache cleared');
  },

  /**
   * Get cached policy for a role
   */
  getCachedPolicy(roleId) {
    return this.policyCache.get(roleId);
  }
};

module.exports = tykPolicyManager;
