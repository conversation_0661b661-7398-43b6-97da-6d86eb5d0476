const { version } = require('../package.json');
const config = require('../config/config');

const swaggerDef = {
  openapi: '3.0.0',
  info: {
    title: 'CareMate API',
    version: version || '1.0.0',
    description: 'CareMate API'
},
  servers: [
    { 
      url: `${config.env === "local" ? `http://localhost:${config.port}` : config.server_url}`,
      description: 'Development server',
    },
  ],
};

module.exports = swaggerDef;
