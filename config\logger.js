const winston = require("winston");
const DailyRotateFile = require("winston-daily-rotate-file");
const axios = require("axios");
const config = require("./config");

// Custom transform: If the logged info is an Error, replace its message with the stack trace.
const enumerateErrorFormat = winston.format((info) => {
  if (info instanceof Error) {
    // Return a new object with the error stack
    return { ...info, message: info.stack };
  }
  return info;
});

// Combined format: adds timestamp, error formatting, splat support, and conditionally colorizes logs.
const customFormat = winston.format.combine(
  winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss" }),
  enumerateErrorFormat(),
  winston.format.splat(), // Enables string interpolation (e.g., logger.info('Hello %s', name))
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message }) => `[${timestamp}] ${level}: ${message}`)
);

// Define arrays for transports, exceptionHandlers, and rejectionHandlers.
let transports = [];
let exceptionHandlers = [];
let rejectionHandlers = [];

// Use file-based logging (with daily rotation) if enabled in the config; otherwise, log only to stdout.
if (config.logger.fileTransport) {
  // Both stdout and file logging.
  transports.push(
    new winston.transports.Console({
      stderrLevels: ["error"],
      format:
        (config.env === "development" || config.env === "local")
          ? winston.format.combine(winston.format.colorize(), customFormat)
          : customFormat,
    }),
    new DailyRotateFile({
      filename: "../logs/api/apilog-%DATE%.log",
      datePattern: "yyyy-MM-DD",
      zippedArchive: false,
      maxSize: config.logger.maxSize,
      maxFiles: config.logger.maxFiles,
      level: config.logger.level,
    })
  );

  // Exception Handlers: Log both to stdout and files.
  exceptionHandlers.push(
    new winston.transports.Console({
      format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
    }),
    new DailyRotateFile({
      filename: "../logs/api/unhandledExceptions-%DATE%.log",
      datePattern: "yyyy-MM-DD",
      zippedArchive: false,
      maxSize: config.logger.maxSize,
      maxFiles: config.logger.maxFiles,
      level: "error",
    })
  );

  // Rejection Handlers: Log both to stdout and files.
  rejectionHandlers.push(
    new winston.transports.Console({
      format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
    }),
    new DailyRotateFile({
      filename: "../logs/api/unhandledRejections-%DATE%.log",
      datePattern: "yyyy-MM-DD",
      zippedArchive: false,
      maxSize: config.logger.maxSize,
      maxFiles: config.logger.maxFiles,
      level: "error",
    })
  );
} else {
  // Log only to stdout.
  transports.push(
    new winston.transports.Console({
      format: winston.format.combine(winston.format.colorize(), customFormat),
    })
  );

  exceptionHandlers.push(
    new winston.transports.Console({
      format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
    })
  );

  rejectionHandlers.push(
    new winston.transports.Console({
      format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
    })
  );
}

/**
 * Custom transport for sending logs to Tyk
 */
class TykTransport extends winston.Transport {
  constructor(options) {
    super(options);
    this.name = 'tyk';
    this.level = options.level || 'info';
  }

  log(info, callback) {
    setImmediate(() => {
      this.emit('logged', info);
    });

    // Send to Tyk if enabled
    if (config.auth.mode === 'tyk') {
      // Dynamically require tyk config to avoid circular dependency
      try {
        const tykConfig = require('./tyk');
        if (tykConfig.isEnabled) {
          this.sendToTyk(info).catch(() => {
            // Silently fail to avoid logging loops
          });
        }
      } catch (error) {
        // Silently fail if tyk config is not available
      }
    }

    callback();
  }

  async sendToTyk(info) {
    // Skip Tyk logging if administrative access is not available
    // This prevents the "invalid or missing key" errors
    return;

    // The following code is commented out until Tyk administrative access is properly configured
    /*
    try {
      // Dynamically require tyk config to avoid circular dependency
      const tykConfig = require('./tyk');

      // Create analytics record format for logs
      const logData = {
        timestamp: new Date().toISOString(),
        method: 'LOG',
        path: '/logs',
        raw_path: '/logs',
        content_length: info.message ? info.message.length : 0,
        user_agent: 'caremate-api-logger',
        day: new Date().getDate(),
        month: new Date().getMonth() + 1,
        year: new Date().getFullYear(),
        hour: new Date().getHours(),
        response_code: info.level === 'error' ? 500 : 200,
        api_key: 'system-logger',
        api_version: '1.0',
        api_name: tykConfig.apiId,
        api_id: tykConfig.apiId,
        org_id: tykConfig.orgId,
        oauth_id: '',
        request_time: 0,
        raw_request: JSON.stringify({
          level: info.level,
          message: info.message,
          meta: info.meta || {},
          service: 'caremate-api',
          auth_mode: config.auth.mode,
          environment: config.env
        }),
        raw_response: '',
        ip_address: '127.0.0.1'
      };

      // Send to Tyk analytics endpoint instead of logs endpoint
      await axios.post(
        `${tykConfig.gatewayUrl}/tyk/analytics/record`,
        logData,
        {
          headers: tykConfig.getGatewayHeaders(),
          timeout: 5000
        }
      );
    } catch (error) {
      // Don't log this error to avoid infinite loops
    }
    */
  }
}

// Add Tyk transport if enabled
if (config.auth.mode === 'tyk') {
  try {
    const tykConfig = require('./tyk');
    if (tykConfig.isEnabled) {
      transports.push(new TykTransport({ level: config.logger.level }));
    }
  } catch (error) {
    // Silently fail if tyk config is not available
  }
}

const logger = winston.createLogger({
  level: config.logger.level,
  format: customFormat,
  transports,
  exceptionHandlers,
  rejectionHandlers,
});

// Add custom methods for Tyk-specific logging
logger.tykInfo = (message, meta = {}) => {
  logger.info(message, { ...meta, tyk: true });
};

logger.tykError = (message, meta = {}) => {
  logger.error(message, { ...meta, tyk: true });
};

logger.tykWarn = (message, meta = {}) => {
  logger.warn(message, { ...meta, tyk: true });
};

logger.authActivity = (identityId, activity, details = {}) => {
  logger.info(`Auth Activity: ${activity}`, {
    identity_id: identityId,
    activity: activity,
    auth_mode: config.auth.mode,
    ...details
  });
};

module.exports = logger;
