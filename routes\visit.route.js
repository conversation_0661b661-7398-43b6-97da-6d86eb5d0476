const express = require("express");
const validate = require("../middlewares/validate");
const VisitValidation = require("../validations/visit.validation");
const VisitController = require("../controllers/visit.controller");

const router = express.Router();

/**
 * @swagger
 * /visits/create-with-guest:
 *   post:
 *     summary: Create a visit, guest, and guest_visit in a single transaction
 *     tags: [Visits]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               facility_id: { type: string, format: uuid }
 *               host_id: { type: string, format: uuid }
 *               escort_id: { type: string, format: uuid }
 *               start_date: { type: string, format: date }
 *               start_time: { type: string }
 *               duration: { type: integer }
 *               guest:
 *                 type: object
 *                 properties:
 *                   first_name: { type: string }
 *                   last_name: { type: string }
 *                   date_of_birth: { type: string, format: date }
 *                   email: { type: string }
 *                   mobile_phone: { type: string }
 *                   image: { type: string }
 *     responses:
 *       201:
 *         description: Visit, Guest, and GuestVisit created successfully
 */
router.post(
  "/create-with-guest",
  validate(VisitValidation.createVisitWithGuest),
  VisitController.createVisitWithGuest
);

/**
 * @swagger
 * /visits/create-event:
 *   post:
 *     summary: Create an event visit with extra fields and optionally link multiple existing guests
 *     tags: [Visits]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - category
 *               - start_date
 *               - end_date
 *               - facility_id
 *               - access_level_id
 *               - host_id
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Board Meeting"
 *               category:
 *                 type: integer
 *                 example: 1
 *               type:
 *                 type: integer
 *                 default: 0
 *                 example: 0
 *               start_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-15"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-15"
 *               repeat_visit:
 *                 type: integer
 *                 example: 0
 *               facility_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               access_level_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               host_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               check_in_instruction:
 *                 type: integer
 *                 example: 1
 *               escort_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               send_notification:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               remind_me:
 *                 type: integer
 *                 example: 1
 *               message_to_visitor:
 *                 type: string
 *                 example: "Please check in at the front desk"
 *               guest_ids:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 example: ["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]
 *                 description: "Array of guest UUIDs to associate with this visit"
 *               status:
 *                 type: integer
 *                 example: 1
 *     responses:
 *       201:
 *         description: Event Visit created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Event Visit created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     visit:
 *                       type: object
 *                       description: "Created visit object"
 *                     guestVisits:
 *                       type: array
 *                       items:
 *                         type: object
 *                       description: "Array of created guest visit associations"
 *       400:
 *         description: Bad request - validation error
 */
router.post(
  "/create-event",
  validate(VisitValidation.createEventVisit),
  VisitController.createEventVisit
);

/**
 * @swagger
 * /visits/{visit_id}/summary:
 *   get:
 *     summary: Get visit summary with host/escort names
 *     tags: [Visits]
 *     parameters:
 *       - in: path
 *         name: visit_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Visit ID
 *     responses:
 *       200:
 *         description: Visit summary retrieved successfully
 */
router.get(
  "/:visit_id/summary",
  validate(VisitValidation.getVisitSummary),
  VisitController.getVisitSummary
);

/**
 * @swagger
 * /visits/{visit_id}:
 *   put:
 *     summary: Update an existing visit
 *     tags: [Visits]
 *     parameters:
 *       - in: path
 *         name: visit_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Visit ID to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Updated Meeting Title"
 *               category:
 *                 type: integer
 *                 example: 1
 *               type:
 *                 type: integer
 *                 example: 0
 *               start_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-15"
 *               end_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-15"
 *               repeat_visit:
 *                 type: integer
 *                 example: 0
 *               facility_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               access_level_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               host_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               check_in_instruction:
 *                 type: integer
 *                 example: 1
 *               escort_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               send_notification:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               remind_me:
 *                 type: integer
 *                 example: 1
 *               message_to_visitor:
 *                 type: string
 *                 example: "Please check in at the front desk"
 *               status:
 *                 type: integer
 *                 example: 1
 *               updated_by:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               guest_ids:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 example: ["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]
 *                 description: "Array of guest UUIDs to associate with this visit (replaces existing associations)"
 *             minProperties: 1
 *     responses:
 *       200:
 *         description: Visit updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Visit updated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     visit:
 *                       type: object
 *                       description: "Updated visit object"
 *                     message:
 *                       type: string
 *                       example: "Visit updated with 2 guest associations"
 *       400:
 *         description: Bad request - validation error
 *       404:
 *         description: Visit not found
 */
router.put(
  "/:visit_id",
  validate(VisitValidation.updateVisit),
  VisitController.updateVisit
);

/**
 * @swagger
 * /visits/{visit_id}:
 *   delete:
 *     summary: Delete a visit and its associated guest visits
 *     tags: [Visits]
 *     parameters:
 *       - in: path
 *         name: visit_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Visit ID to delete
 *     responses:
 *       200:
 *         description: Visit deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Visit deleted successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     visit_id:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *                     message:
 *                       type: string
 *                       example: "Visit and associated guest visits have been deleted"
 *       404:
 *         description: Visit not found
 *       400:
 *         description: Bad request - deletion failed
 */
router.delete(
  "/:visit_id",
  validate(VisitValidation.deleteVisit),
  VisitController.deleteVisit
);

/**
 * @swagger
 * /visits/{visit_id}/guest/{guest_id}/checkin:
 *   post:
 *     summary: Check-in a guest for a visit
 *     tags: [Visits]
 *     parameters:
 *       - in: path
 *         name: visit_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Visit ID
 *       - in: path
 *         name: guest_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Guest ID
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               updated_by:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *                 description: "ID of the user performing the check-in"
 *               check_in_time:
 *                 type: string
 *                 format: date-time
 *                 example: "2024-01-15T10:30:00.000Z"
 *                 description: "Check-in time (optional, defaults to current time)"
 *     responses:
 *       200:
 *         description: Guest checked in successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Guest checked in successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     guestVisit:
 *                       type: object
 *                       description: "Updated guest visit object with check-in details"
 *                     checkinRecord:
 *                       type: object
 *                       description: "Visit guest checkin record"
 *                     guest_pin:
 *                       type: string
 *                       example: "123456"
 *                       description: "6-digit PIN generated for the guest"
 *       400:
 *         description: Bad request - Guest already checked in or validation error
 *       404:
 *         description: Guest visit association not found
 */
router.post(
  "/:visit_id/guest/:guest_id/checkin",
  validate(VisitValidation.checkInGuest),
  VisitController.checkInGuest
);

/**
 * @swagger
 * /visits/{visit_id}/guest/{guest_id}/checkout:
 *   post:
 *     summary: Check-out a guest from a visit
 *     tags: [Visits]
 *     parameters:
 *       - in: path
 *         name: visit_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Visit ID
 *       - in: path
 *         name: guest_id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Guest ID
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               updated_by:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *                 description: "ID of the user performing the check-out"
 *               check_out_time:
 *                 type: string
 *                 format: date-time
 *                 example: "2024-01-15T12:30:00.000Z"
 *                 description: "Check-out time (optional, defaults to current time)"
 *     responses:
 *       200:
 *         description: Guest checked out successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Guest checked out successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     guestVisit:
 *                       type: object
 *                       description: "Updated guest visit object with check-out details"
 *                     checkinRecord:
 *                       type: object
 *                       description: "Updated visit guest checkin record with check-out time"
 *       400:
 *         description: Bad request - Guest not checked in or validation error
 *       404:
 *         description: Guest visit association not found
 */
router.post(
  "/:visit_id/guest/:guest_id/checkout",
  validate(VisitValidation.checkOutGuest),
  VisitController.checkOutGuest
);

module.exports = router;
