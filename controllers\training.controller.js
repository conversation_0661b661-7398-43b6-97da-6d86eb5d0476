const httpStatus = require("http-status");
const { Training, Identity } = require("../models");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { paginate } = require("../models/plugins/paginate.plugin");
const { Op } = require("sequelize");

/**
 * @desc    Create a new training record
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the created training
 */
exports.createTraining = catchAsync(async (req, res) => {
  const cleanedData = { ...req.body };
  const dateFields = ['due_date', 'date_completed'];
  dateFields.forEach(field => {
    if (cleanedData[field] === '') {
      cleanedData[field] = null;
    }
  });
  const training = await Training.create(cleanedData);
  sendSuccess(res, "Training created successfully", httpStatus.CREATED, training);
});

/**
 * @desc    Get all training records with pagination, sorting, and filtering
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing paginated training records
 */
exports.getTrainings = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, sortBy = "created_at", sortOrder = "DESC", search, identity_id, status, category } = req.query;
  const paginationOptions = { page, limit, sortBy, sortOrder };

  const queryOptions = {
    order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
    include: [
      {
        model: Identity,
        as: "identity",
        attributes: ["identity_id", "first_name", "last_name", "email"],
      },
    ],
  };

  // Build where conditions
  const whereConditions = {};

  if (identity_id) {
    whereConditions.identity_id = identity_id;
  }

  if (status) {
    whereConditions.status = status;
  }

  if (category) {
    whereConditions.category = category;
  }

  if (search) {
    whereConditions[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { course_number: { [Op.iLike]: `%${search}%` } },
      { category: { [Op.iLike]: `%${search}%` } },
      { course_type: { [Op.iLike]: `%${search}%` } },
    ];
  }

  if (Object.keys(whereConditions).length > 0) {
    queryOptions.where = whereConditions;
  }

  const result = await paginate(Training, queryOptions, paginationOptions);
  sendSuccess(res, "Training records retrieved successfully", httpStatus.OK, result);
});

/**
 * @desc    Get a single training record by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the training record
 */
exports.getTrainingById = catchAsync(async (req, res) => {
  const { id } = req.params;

  const training = await Training.findByPk(id, {
    include: [
      {
        model: Identity,
        as: "identity",
        attributes: ["identity_id", "first_name", "last_name", "email"],
      },
    ],
  });

  if (!training) {
    return sendError(res, "Training record not found", httpStatus.NOT_FOUND);
  }

  sendSuccess(res, "Training record retrieved successfully", httpStatus.OK, training);
});

/**
 * @desc    Update a training record by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the updated training record
 */
exports.updateTraining = catchAsync(async (req, res) => {
  const { training_id } = req.params;

  const training = await Training.findByPk(training_id);
  if (!training) {
    return sendError(res, "Training record not found", httpStatus.NOT_FOUND);
  }

  // Clean up date fields - convert empty strings to null
  const cleanedData = { ...req.body };
  const dateFields = ['due_date', 'date_completed'];

  dateFields.forEach(field => {
    if (cleanedData[field] === '') {
      cleanedData[field] = null;
    }
  });

  await training.update(cleanedData);
  sendSuccess(res, "Training record updated successfully", httpStatus.OK, training);
});

/**
 * @desc    Delete a training record by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object confirming deletion
 */
exports.deleteTraining = catchAsync(async (req, res) => {
  const { training_id } = req.params;

  const training = await Training.findByPk(training_id);
  if (!training) {
    return sendError(res, "Training record not found", httpStatus.NOT_FOUND);
  }

  await training.destroy();
  sendSuccess(res, "Training record deleted successfully", httpStatus.OK, { training_id: training_id });
});

