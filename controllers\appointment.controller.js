const { Appointment, Patient, PatientAppointmentView, GlobalConfiguration, PatientAppointmentGuestView, OutpatientGuestFutureView, OutpatientGuestCurrentView, OutpatientCheckin } = require("../models");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const httpStatus = require("http-status");
const { Op, literal } = require("sequelize");
const { paginate } = require("../models/plugins/paginate.plugin");



/**
 * Get today's patient appointments, with only four fields:
 *   • appointment_id
 *   • appointment_date
 *   • patient_name  (first + ' ' + last)
 *   • image
 *
 * @query {string} [search]  filter on patient name, department, or provider
 * @query {number} [type=0]  appointment type key
 */

exports.search = catchAsync(async (req, res) => {
  const { search = "", type = "0", facility_id } = req.query;

  // Define start and end of day for filtering
  const startOfDay = new Date();
  startOfDay.setHours(0, 0, 0, 0);
  const startOfTomorrow = new Date(startOfDay);
  startOfTomorrow.setDate(startOfTomorrow.getDate() + 1);

  // Base where clause

  const where = {
    type: Number(type),
  };

  // If type filter is not "0", only today's appointments
  if (Number(type) === 0) {
    where.appointment_status = 1;
  }
  // If type filter is not "0", only today's appointments
  if (Number(type) !== 0) {
    where.appointment_date = {
      [Op.gte]: startOfDay,
      [Op.lt]: startOfTomorrow,
    };
  }
  // Search across patient name, department, provider
  if (search) {
    where[Op.or] = [
      { first_name: { [Op.iLike]: `%${search}%` } },
      { last_name: { [Op.iLike]: `%${search}%` } },
      { mrn: { [Op.iLike]: `%${search}%` } },
      { preferred_name: { [Op.iLike]: `%${search}%` } },
    ];
  }
  // Filter by facility_id if provided
  if (facility_id) {
    where.facility_id = facility_id;
  }

  // Fetch appointments with exactly five fields, including concatenated patient_name
  const appointments = await PatientAppointmentView.findAll({
    where,
    raw: true,
    attributes: [
      "appointment_id",
      "appointment_date",
      "patient_name",
      "image",
      "mrn",
      "patient_id",
      "preferred_name",
      "birth_date",
      "facility_id"

    ],
    order: [["appointment_date", "DESC"]],
  });

  const formattedAppointments = appointments.map(appointment => ({
    ...appointment,
    birth_date: appointment.birth_date ? new Date(appointment.birth_date).toISOString().split('T')[0] : null
  }));

  return sendSuccess(
    res,
    "Today's appointments retrieved successfully",
    httpStatus.OK,
    formattedAppointments
  );
});

/**
 * Get full details of a single appointment by its ID.
 *
 * Excludes: patient_id, type, updated_by, created_at, updated_at
 *
 * @param {string} req.params.id  UUID of the appointment
 * @returns {Object} 200 – Appointment object with all fields except the excluded ones
 *
 * @throws {Error} 404 – If no appointment is found with the given ID
 * @throws {Error} 500 – On any other server error
 */
exports.show = catchAsync(async (req, res) => {
  const { appointment_id } = req.params;
  const appointment = await PatientAppointmentView.findByPk(appointment_id, {
    attributes: {
      exclude: [
        "type",
        "updated_by",
        "created_at",
        "updated_at",
        "postal_code",
        "city",
        "address_line_1",
        "address_line_2",
        "state",
        "country",
      ],
    },
  });
  if (appointment && appointment.room_number === null) {
    appointment.room_number = "N/A";
  }

  const config = await GlobalConfiguration.findOne({
    where: { name: 'patient_max_visitors' },
  });
  const maxAllowedVisitors = config ? parseInt(config.value, 10) : 0;

  // Count guests who are checked-in for this appointment
  const checkedInVisitors = await PatientAppointmentGuestView.count({
    where: {
      appointment_id,
      appointment_guest_status_name: 'Checked-In', // make sure this matches actual data
    },
  });

  // Append visitor stats to the appointment object
  const appointmentWithVisitorStats = {
    ...appointment.toJSON(),
    max_allowed_visitors: maxAllowedVisitors,
    checked_in_visitors: checkedInVisitors,
  };

  return sendSuccess(
    res,
    "Appointment retrieved successfully",
    httpStatus.OK,
    appointmentWithVisitorStats
  );
});
/**
 * Get full details of a single appointment by patient ID and current appointment date.
 *
 * Excludes: patient_id, type, updated_by, created_at, updated_at
 *
 * @param {string} req.params.patient_id  UUID of the patient
 * @param {string} req.query.appointment_date  Date of the appointment (in a suitable format)
 * @returns {Object} 200 – Appointment object with all fields except the excluded ones
 *
 * @throws {Error} 404 – If no appointment is found with the given patient ID and date
 * @throws {Error} 500 – On any other server error
 */
exports.outpatient = catchAsync(async (req, res) => {
  const { search = "", page = 1, limit = 10, sortBy = "appointment_date", sortOrder = "ASC" } = req.query;
  const { patient_id } = req.params;
  const paginationOptions = { page, limit, sortBy, sortOrder };

  const where = { patient_id };
  if (search) {
    where[Op.or] = [
      literal(`(first_name || ' ' || last_name) ILIKE '%${search}%'`),
    ];
  }

  const queryOptions = {
    where,
    order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
  };

  const result = await paginate(
    OutpatientGuestCurrentView,
    queryOptions,
    paginationOptions
  );

  if (result.totalItems === 0) {
    return sendError(res, "No outpatient appointments found", httpStatus.NOT_FOUND);
  }

  return sendSuccess(
    res,
    "Outpatient appointments retrieved successfully",
    httpStatus.OK,
    result
  );
});

exports.future = catchAsync(async (req, res) => {
  const {
    search = "",
    page = 1,
    limit = 10,
    sortBy = "appointment_date",
    sortOrder = "ASC",
  } = req.query;

  const { patient_id } = req.params;
  const paginationOptions = { page, limit, sortBy, sortOrder };

  // Build base where clause
  const where = { patient_id };

  if (search) {
    where[Op.or] = [
      literal(`(first_name || ' ' || last_name) ILIKE '%${search}%'`),
    ];
  }

  // Manually fetch the full data set
  const fullData = await OutpatientGuestFutureView.findAll({
    where,
    order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
    raw: true,
  });

  // Then paginate the plain array
  const result = await paginate(fullData, {}, paginationOptions);

  if (result.totalItems === 0) {
    return sendError(res, "No future appointments found", httpStatus.NOT_FOUND);
  }

  return sendSuccess(
    res,
    "Future appointments retrieved successfully",
    httpStatus.OK,
    result
  );
});



exports.checkPatient = catchAsync(async (req, res) => {
  const { appointment_id } = req.params;
  const { action } = req.query;

  const appointment = await Appointment.findByPk(appointment_id);
  if (!appointment) {
    return sendError(res, "Appointment not found", httpStatus.BAD_REQUEST);
  }

  if (appointment.type !== 1) {
    return sendError(
      res,
      "Check-in and check-out are only allowed for appointments of type 1",
      httpStatus.FORBIDDEN
    );
  }

  if (action === "checkIn") {
    appointment.status = 3; // Checked-In
    appointment.arrival_time = new Date();
    await appointment.save();

    // Create a check-in record in OutpatientCheckin
    await OutpatientCheckin.create({
      appointment_id: appointment_id,
      checkin_time: new Date(),
    });

    return sendSuccess(
      res,
      "Patient checked in successfully",
      httpStatus.OK,
      {
        appointment_id,
        status: "Checked-In",
      }
    );
  } else if (action === "checkOut") {
    appointment.status = 4; // Checked-Out
    appointment.departure_time = new Date();
    await appointment.save();

    // Update the checkout time in the OutpatientCheckin table
    const checkinRecord = await OutpatientCheckin.findOne({
      where: { appointment_id },
      order: [["createdAt", "DESC"]], // Get the latest check-in record
    });

    if (checkinRecord) {
      checkinRecord.checkout_time = new Date();
      await checkinRecord.save();
    }

    return sendSuccess(
      res,
      "Patient checked out successfully",
      httpStatus.OK,
      {
        appointment_id,
        status: "Checked-Out",
      }
    );
  } else {
    return sendError(
      res,
      "Invalid action. Use 'checkIn' or 'checkOut'.",
      httpStatus.BAD_REQUEST
    );
  }
});

/**
 * Update the image for a patient guest.
 *
 * @param {string} patient_id
 * @body {string} image
 * @returns {Promise<void>}
 */
exports.image = catchAsync(async (req, res) => {
  const { patient_id } = req.params;

  const patient = await Patient.findByPk(patient_id);
  if (!patient) {
    return sendError(res, "Patient not found", httpStatus.NOT_FOUND);
  }
  const { image } = req.body;
  if (!image) {
    return sendError(res, "Image data is required", httpStatus.BAD_REQUEST);
  }
  patient.image = image;
  await patient.save();
  return sendSuccess(
    res,
    "Guest image updated successfully",
    httpStatus.OK,
    patientGuest
  );
});
