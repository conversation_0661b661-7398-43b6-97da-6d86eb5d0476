const httpStatus = require("http-status");
const { Vehicle, Identity } = require("../models");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { paginate } = require("../models/plugins/paginate.plugin");
const { Op } = require("sequelize");

/**
 * @desc    Create a new vehicle
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the created vehicle
 */
exports.createVehicle = catchAsync(async (req, res) => {
  // Clean up date fields - convert empty strings to null
  const cleanedData = { ...req.body };
  const dateFields = ['uploaded_date'];

  dateFields.forEach(field => {
    if (cleanedData[field] === '') {
      cleanedData[field] = null;
    }
  });

  const vehicle = await Vehicle.create(cleanedData);
  sendSuccess(res, "Vehicle created successfully", httpStatus.CREATED, vehicle);
});

/**
 * @desc    Get all vehicles with pagination, sorting, and filtering
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing paginated vehicles
 */
exports.getVehicles = catchAsync(async (req, res) => {
  const { page = 1, limit = 10, sortBy = "created_at", sortOrder = "DESC", search, identity_id, make, model, year } = req.query;
  const paginationOptions = { page, limit, sortBy, sortOrder };

  const queryOptions = {
    order: [[sortBy, sortOrder.toUpperCase() === "ASC" ? "ASC" : "DESC"]],
    include: [
      {
        model: Identity,
        as: "identity",
        attributes: ["identity_id", "first_name", "last_name", "email"],
      },
    ],
  };

  // Build where conditions
  const whereConditions = {};

  if (identity_id) {
    whereConditions.identity_id = identity_id;
  }

  if (make) {
    whereConditions.make = { [Op.iLike]: `%${make}%` };
  }

  if (model) {
    whereConditions.model = { [Op.iLike]: `%${model}%` };
  }

  if (year) {
    whereConditions.year = year;
  }

  if (search) {
    whereConditions[Op.or] = [
      { plate_number: { [Op.iLike]: `%${search}%` } },
      { vin: { [Op.iLike]: `%${search}%` } },
      { make: { [Op.iLike]: `%${search}%` } },
      { model: { [Op.iLike]: `%${search}%` } },
      { color: { [Op.iLike]: `%${search}%` } },
      { issued_by: { [Op.iLike]: `%${search}%` } },
    ];
  }

  if (Object.keys(whereConditions).length > 0) {
    queryOptions.where = whereConditions;
  }

  const result = await paginate(Vehicle, queryOptions, paginationOptions);
  sendSuccess(res, "Vehicles retrieved successfully", httpStatus.OK, result);
});

/**
 * @desc    Get a single vehicle by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the vehicle
 */
exports.getVehicleById = catchAsync(async (req, res) => {
  const { vehicle_id } = req.params;
  const vehicle = await Vehicle.findByPk(vehicle_id, {
    include: [
      {
        model: Identity,
        as: "identity",
        attributes: ["identity_id", "first_name", "last_name", "email"],
      },
    ],
  });
  if (!vehicle) {
    return sendError(res, "Vehicle not found", httpStatus.NOT_FOUND);
  }

  sendSuccess(res, "Vehicle retrieved successfully", httpStatus.OK, vehicle);
});

/**
 * @desc    Update a vehicle by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing the updated vehicle
 */
exports.updateVehicle = catchAsync(async (req, res) => {
  const { vehicle_id } = req.params;
  const vehicle = await Vehicle.findByPk(vehicle_id);
  if (!vehicle) {
    return sendError(res, "Vehicle not found", httpStatus.NOT_FOUND);
  }
  const cleanedData = { ...req.body };
  const dateFields = ['uploaded_date'];
  dateFields.forEach(field => {
    if (cleanedData[field] === '') {
      cleanedData[field] = null;
    }
  });
  await vehicle.update(cleanedData);
  sendSuccess(res, "Vehicle updated successfully", httpStatus.OK, vehicle);
});

/**
 * @desc    Delete a vehicle by ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object confirming deletion
 */
exports.deleteVehicle = catchAsync(async (req, res) => {
  const { vehicle_id } = req.params;
  const vehicle = await Vehicle.findByPk(vehicle_id);
  if (!vehicle) {
    return sendError(res, "Vehicle not found", httpStatus.NOT_FOUND);
  }
  await vehicle.destroy();
  sendSuccess(res, "Vehicle deleted successfully", httpStatus.OK, { vehicle_id: vehicle_id });
});


