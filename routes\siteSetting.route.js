const express = require("express");
const validate = require("../middlewares/validate");
const { SiteSettingValidation } = require("../validations");
const { SiteSettingController } = require("../controllers");
const auth = require("../middlewares/auth");

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Site Settings
 *   description: Site Settings management (refresh and remove cache)
 */

/**
 * @swagger
 * /site-setting/cache:
 *   post:
 *     summary: Refresh or remove cache entries
 *     tags: [Site Settings]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               operation:
 *                 type: string
 *                 enum: [refresh, remove]
 *                 default: refresh
 *                 example: refresh
 *               keys:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["masterData:countries", "eventConfig:user_created"]
 *                 description: >-
 *                   List of cache keys to operate on.
 *                   Valid patterns - ["eventConfig:*", "masterData:*"].
 *             required:
 *               - keys
 *     responses:
 *       200:
 *         description: Success message based on operation
 */
router.post(
  "/cache",
  //   auth("modify_cache"),
  validate(SiteSettingValidation.setCache),
  SiteSettingController.setCache
);

/**
 * @swagger
 * /site-setting/cache:
 *   get:
 *     summary: Retrieve cached data by key(s)
 *     tags: [Site Settings]
 *     parameters:
 *       - in: query
 *         name: keys
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *           example: ["masterData:facility_type", "eventConfig:facilityStatusActive"]
 *         explode: true
 *         description: >-
 *           Valid patterns - ["eventConfig:*", "masterData:*"].
 *     responses:
 *       200:
 *         description: Cached data structure
 *         content:
 *           application/json:
 *             example:
 *               data:
 *                 "masterData:countries": [{"key": "US", "value": "United States"}]
 *                 "eventConfig:user_created": {"rules": [...]}
 */
router.get(
  "/cache",
  // auth("view_cache"),
  validate(SiteSettingValidation.getCache),
  SiteSettingController.getCache
);

module.exports = router;
