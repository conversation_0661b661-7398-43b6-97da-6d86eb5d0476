const morgan = require('morgan');
const axios = require('axios');
const config = require('./config');
const logger = require('./logger');
const tykConfig = require('./tyk');

morgan.token('message', (req, res) => res.locals.errorMessage || '');
morgan.token('identity', (req, res) => req.identity?.identity_id || 'anonymous');
morgan.token('auth-mode', (req, res) => config.auth.mode);

// Custom token to capture request data for analytics
morgan.token('analytics-data', (req, res) => {
  if (config.auth.mode === 'tyk' && tykConfig.isEnabled) {
    const analyticsData = {
      method: req.method,
      url: req.originalUrl,
      status: res.statusCode,
      responseTime: res.get('X-Response-Time'),
      identity: req.identity?.identity_id || 'anonymous',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      authMode: config.auth.mode,
      error: res.locals.errorMessage
    };

    // Send to Tyk Analytics (fire and forget)
    sendToTykAnalytics(analyticsData).catch(() => {});
  }
  return ''; // Return empty string as this token is just for side effects
});

const getIpFormat = () => (config.env === 'production' ? ':remote-addr - ' : '');
const successResponseFormat = `${getIpFormat()}:method :url :status - :response-time ms - identity: :identity - auth: :auth-mode:analytics-data`;
const errorResponseFormat = `${getIpFormat()}:method :url :status - :response-time ms - identity: :identity - auth: :auth-mode - message: :message:analytics-data`;

/**
 * Send logs to Tyk Analytics if Tyk mode is enabled
 * Note: This is disabled if Tyk administrative APIs are not available
 */
const sendToTykAnalytics = async (logData) => {
  if (config.auth.mode !== 'tyk' || !tykConfig.isEnabled) {
    return;
  }

  // Skip analytics if administrative access is not available
  // This prevents the "invalid or missing key" errors
  logger.debug('Tyk analytics logging skipped - administrative API access not configured');
  return;

  // The following code is commented out until Tyk administrative access is properly configured
  /*
  try {
    const analyticsData = {
      timestamp: new Date().toISOString(),
      method: logData.method,
      path: logData.url,
      raw_path: logData.url,
      content_length: 0,
      user_agent: logData.userAgent,
      day: new Date().getDate(),
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
      hour: new Date().getHours(),
      response_code: logData.status,
      api_key: logData.identity || 'anonymous',
      api_version: '1.0',
      api_name: tykConfig.apiId,
      api_id: tykConfig.apiId,
      org_id: tykConfig.orgId,
      oauth_id: '',
      request_time: parseInt(logData.responseTime) || 0,
      raw_request: '',
      raw_response: '',
      ip_address: logData.ip
    };

    await axios.post(
      `${tykConfig.gatewayUrl}/tyk/analytics/record`,
      analyticsData,
      {
        headers: tykConfig.getGatewayHeaders(),
        timeout: 5000
      }
    );
  } catch (error) {
    logger.warn('Failed to send analytics to Tyk:', error.message);
  }
  */
};

const successHandler = morgan(successResponseFormat, {
  skip: (req, res) => res.statusCode >= 400,
  stream: {
    write: (message) => {
      logger.info(message.trim());
      // Analytics data is captured via the analytics-data token
      // No need to access req/res here as they're not available
    }
  },
});

const errorHandler = morgan(errorResponseFormat, {
  skip: (req, res) => res.statusCode < 400,
  stream: {
    write: (message) => {
      logger.error(message.trim());
      // Analytics data is captured via the analytics-data token
      // No need to access req/res here as they're not available
    }
  },
});

module.exports = {
  successHandler,
  errorHandler,
};
