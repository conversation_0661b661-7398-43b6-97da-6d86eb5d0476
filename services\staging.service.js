const crypto = require('crypto');
const { StagingData } = require('../models');
const { Op } = require('sequelize');
const logger = require('../config/logger');

/**
 * Calculate SHA-256 hash of record data for change detection
 * @param {Object} record - The CSV record data
 * @returns {string} - SHA-256 hash
 */
const calculateRecordHash = (record) => {
  // Create a normalized string representation of the record
  // Sort keys to ensure consistent hashing regardless of property order
  const sortedKeys = Object.keys(record).sort();
  const normalizedData = {};

  sortedKeys.forEach(key => {
    // Normalize values: trim strings, convert to lowercase for case-insensitive comparison
    let value = record[key];
    if (typeof value === 'string') {
      value = value.trim().toLowerCase();
    }
    normalizedData[key] = value;
  });

  const dataString = JSON.stringify(normalizedData);
  return crypto.createHash('sha256').update(dataString).digest('hex');
};

// Removed getFileModifiedTime function as file_modified_at column was removed

/**
 * Check if records exist in staging and return only new/updated ones
 * @param {Array} records - Array of CSV records
 * @param {Object} agent - Agent configuration
 * @param {Object} performanceMonitor - Performance monitoring instance
 * @returns {Promise<Array>} - Array of new/updated records
 */
const filterNewAndUpdatedRecords = async (records, agent, performanceMonitor = null) => {
  try {
    // Get the dynamic staging key from agent configuration
    const stagingKey = agent.stagging_key;

    // Step 1: Prepare all records with hashes and identifier values
    const hashingStepName = 'Record Hashing and Validation';
    performanceMonitor?.startStep(hashingStepName, {
      totalRecords: records.length,
      agentId: agent.agent_id,
      stagingKey: stagingKey
    });

    const processedRecords = [];
    const validRecords = [];
    let invalidRecords = 0;

    for (const record of records) {
      const identifierValue = record[stagingKey]?.toString()?.trim()?.toLowerCase();
      if (!identifierValue) {
        logger.warn(`Skipping record without ${stagingKey}:`, record);
        invalidRecords++;
        continue;
      }

      const recordHash = calculateRecordHash(record);
      const processedRecord = {
        original: record,
        identifierValue,
        recordHash,
        stagingData: {
          agent_id: agent.agent_id,
          record_hash: recordHash,
          identifier: identifierValue, // Using identifier column to store dynamic staging key value
          original_data: record,
          processing_status: 'pending'
        }
      };

      processedRecords.push(processedRecord);
      validRecords.push(record);
    }

    performanceMonitor?.endStep(hashingStepName, {
      validRecords: processedRecords.length,
      invalidRecords,
      validationRate: `${Math.round((processedRecords.length / records.length) * 100)}%`,
      stagingKey: stagingKey
    });

    if (processedRecords.length === 0) {
      return [];
    }

    // Step 2: Fetch all existing staging records for this agent in one query
    const dbQueryStepName = 'Database Query - Existing Records';
    performanceMonitor?.startStep(dbQueryStepName, {
      identifiersToQuery: processedRecords.length,
      agentId: agent.agent_id,
      stagingKey: stagingKey
    });

    const identifierValues = processedRecords.map(r => r.identifierValue);
    const existingStagingRecords = await StagingData.findAll({
      where: {
        agent_id: agent.agent_id,
        identifier: { // Using identifier column to store dynamic staging key values
          [Op.in]: identifierValues
        }
      },
      order: [['identifier', 'ASC'], ['created_at', 'DESC']]
    });

    performanceMonitor?.endStep(dbQueryStepName, {
      recordsFound: existingStagingRecords.length,
      queryEfficiency: `${existingStagingRecords.length}/${processedRecords.length} ${stagingKey} values have existing records`
    });

    // Step 3: Create lookup maps for efficient filtering
    const mappingStepName = 'Record Mapping and Filtering';
    performanceMonitor?.startStep(mappingStepName, {
      existingRecords: existingStagingRecords.length,
      newRecords: processedRecords.length
    });

    const existingByIdentifierAndHash = new Map();
    const latestByIdentifier = new Map();

    existingStagingRecords.forEach(existing => {
      const key = `${existing.identifier}:${existing.record_hash}`; // identifier column stores dynamic staging key value
      existingByIdentifierAndHash.set(key, existing);

      if (!latestByIdentifier.has(existing.identifier)) { // identifier column stores dynamic staging key value
        latestByIdentifier.set(existing.identifier, existing);
      }
    });

    // Step 4: Filter records to find new/updated ones
    const recordsToCreate = [];
    const newAndUpdatedRecords = [];
    let duplicateRecords = 0;
    let updatedRecords = 0;
    let newRecords = 0;

    for (const processedRecord of processedRecords) {
      const { original, identifierValue, recordHash, stagingData } = processedRecord;
      const lookupKey = `${identifierValue}:${recordHash}`;

      // Check if this exact record already exists
      if (existingByIdentifierAndHash.has(lookupKey)) {
        logger.debug(`Record with ${stagingKey} ${identifierValue} and hash ${recordHash} already exists in staging, skipping`);
        duplicateRecords++;
        continue;
      }

      // Check if there's a different version of this record
      const existingRecord = latestByIdentifier.get(identifierValue);
      if (existingRecord && existingRecord.record_hash !== recordHash) {
        logger.info(`Found updated record for ${stagingKey} ${identifierValue}, old hash: ${existingRecord.record_hash}, new hash: ${recordHash}`);
        updatedRecords++;
      } else if (!existingRecord) {
        logger.info(`Found new record for ${stagingKey} ${identifierValue}`);
        newRecords++;
      }

      recordsToCreate.push(stagingData);
      newAndUpdatedRecords.push(original);
    }

    performanceMonitor?.endStep(mappingStepName, {
      newRecords,
      updatedRecords,
      duplicateRecords,
      totalToCreate: recordsToCreate.length,
      filteringEfficiency: `${Math.round((duplicateRecords / processedRecords.length) * 100)}% duplicates filtered out`
    });

    // Step 5: Bulk create all new staging records
    if (recordsToCreate.length > 0) {
      const bulkCreateStepName = 'Database Bulk Insert';
      performanceMonitor?.startStep(bulkCreateStepName, {
        recordsToInsert: recordsToCreate.length
      });

      await StagingData.bulkCreate(recordsToCreate);

      performanceMonitor?.endStep(bulkCreateStepName, {
        recordsInserted: recordsToCreate.length,
        insertRate: `${Math.round((recordsToCreate.length / records.length) * 100)}% of original records inserted`
      });

      logger.info(`Bulk created ${recordsToCreate.length} staging records`);
    }

    return newAndUpdatedRecords;

  } catch (error) {
    logger.error(`Error in filterNewAndUpdatedRecords: ${error.message}`, { error });
    throw error;
  }
};

/**
 * Mark records as processed in staging
 * @param {Array} records - Array of successfully processed records
 * @param {Object} agent - Agent configuration
 * @returns {Promise<void>}
 */
const markRecordsAsProcessed = async (records, agent) => {
  try {
    if (records.length === 0) {
      return;
    }

    // Get the dynamic staging key from agent configuration
    const stagingKey = agent.stagging_key;

    // Prepare record identifiers for bulk update
    const recordIdentifiers = [];

    for (const record of records) {
      const identifierValue = record[stagingKey]?.toString()?.trim()?.toLowerCase();
      if (!identifierValue) continue;

      const recordHash = calculateRecordHash(record);
      recordIdentifiers.push({
        identifier: identifierValue,
        record_hash: recordHash
      });
    }

    if (recordIdentifiers.length === 0) {
      return;
    }

    // Build OR conditions for bulk update
    const whereConditions = recordIdentifiers.map(identifier => ({
      agent_id: agent.agent_id,
      identifier: identifier.identifier,
      record_hash: identifier.record_hash,
      processing_status: 'pending'
    }));

    // Perform bulk update
    const [updatedCount] = await StagingData.update(
      {
        processing_status: 'processed',
        processed_at: new Date(),
        error_message: null
      },
      {
        where: {
          [Op.or]: whereConditions
        }
      }
    );

    logger.info(`Bulk marked ${updatedCount} records as processed in staging`);

  } catch (error) {
    logger.error(`Error in markRecordsAsProcessed: ${error.message}`, { error });
    throw error;
  }
};

/**
 * Mark records as failed in staging
 * @param {Array} failedRecords - Array of failed records with error info
 * @param {Object} agent - Agent configuration
 * @returns {Promise<void>}
 */
const markRecordsAsFailed = async (failedRecords, agent) => {
  try {
    if (failedRecords.length === 0) {
      return;
    }

    // Get the dynamic staging key from agent configuration
    const stagingKey = agent.stagging_key;

    // Group failed records by error message for efficient bulk updates
    const errorGroups = new Map();

    for (const failedRecord of failedRecords) {
      const { record, error } = failedRecord;
      const identifierValue = record[stagingKey]?.toString()?.trim()?.toLowerCase();
      if (!identifierValue) continue;

      const recordHash = calculateRecordHash(record);
      const errorMessage = error || 'Unknown error';

      if (!errorGroups.has(errorMessage)) {
        errorGroups.set(errorMessage, []);
      }

      errorGroups.get(errorMessage).push({
        identifier: identifierValue,
        record_hash: recordHash
      });
    }

    // Perform bulk updates for each error group
    for (const [errorMessage, recordIdentifiers] of errorGroups) {
      if (recordIdentifiers.length === 0) continue;

      const whereConditions = recordIdentifiers.map(identifier => ({
        agent_id: agent.agent_id,
        identifier: identifier.identifier,
        record_hash: identifier.record_hash,
        processing_status: 'pending'
      }));

      const [updatedCount] = await StagingData.update(
        {
          processing_status: 'failed',
          error_message: errorMessage
        },
        {
          where: {
            [Op.or]: whereConditions
          }
        }
      );

      logger.info(`Bulk marked ${updatedCount} records as failed in staging with error: ${errorMessage}`);
    }

  } catch (error) {
    logger.error(`Error in markRecordsAsFailed: ${error.message}`, { error });
    throw error;
  }
};

module.exports = {
  calculateRecordHash,
  filterNewAndUpdatedRecords,
  markRecordsAsProcessed,
  markRecordsAsFailed
};
