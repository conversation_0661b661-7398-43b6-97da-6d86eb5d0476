const express = require("express");
const validate = require("../middlewares/validate");
const StateValidation = require("../validations/state.validation");
const StateController = require("../controllers/state.controller");
const auth = require("../middlewares/auth");

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: States
 *   description: State management
 */

router
  .route("/")
  /**
   * @swagger
   * /states:
   *   get:
   *     summary: Get all states
   *     tags: [States]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: List of states
   */
  .get(auth("view_states"), StateController.index)
  /**
   * @swagger
   * /states:
   *   post:
   *     summary: Create a new state
   *     tags: [States]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               country_id:
   *                 type: string
   *                 description: ID of the country
   *                 example: 123e4567-e89b-12d3-a456-426614174000
   *               name:
   *                 type: string
   *                 description: Name of the state
   *                 example: California
   *     responses:
   *       201:
   *         description: State created
   */
  .post(auth("create_state"), validate(StateValidation.create), StateController.create);

router
  .route("/:stateId")
  /**
   * @swagger
   * /states/{stateId}:
   *   get:
   *     summary: Get a state by ID
   *     tags: [States]
   *     parameters:
   *       - name: stateId
   *         in: path
   *         required: true
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: State details
   */
  .get(auth("view_state"), validate(StateValidation.state), StateController.show)
  /**
   * @swagger
   * /states/{stateId}:
   *   patch:
   *     summary: Update a state
   *     tags: [States]
   *     parameters:
   *       - name: stateId
   *         in: path
   *         required: true
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: State updated
   */
  .patch(auth("edit_state"), validate(StateValidation.update), StateController.update)
  /**
   * @swagger
   * /states/{stateId}:
   *   delete:
   *     summary: Delete a state
   *     tags: [States]
   *     parameters:
   *       - name: stateId
   *         in: path
   *         required: true
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       204:
   *         description: State deleted
   */
  .delete(auth("delete_state"), validate(StateValidation.state), StateController.delete);

module.exports = router;