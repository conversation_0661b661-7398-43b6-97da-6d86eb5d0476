const Joi = require("joi");
const { existsMasterData, exists } = require("./custom.validation");
const { de } = require("@faker-js/faker");
const { status } = require("../controllers/facility.controller");
const { email } = require("../config/config");
const { getModelAttributes } = require("../helpers/global.helper");
const {
  PatientAppointmentGuestView,
  PatientGuest,
  AppointmentGuest,
  PatientGuestHistoryView,
} = require("../models");

const guestAttributes = getModelAttributes(PatientGuest);
const guestHistoryAttributes = getModelAttributes(PatientGuestHistoryView);
const patientGuestAttributes = getModelAttributes(PatientAppointmentGuestView);

const appointmentGuestAttributes = [
  "first_name",
  "last_name",
  "mrn",
  "guest_image",
  "guest_arrival_time",
  "guest_departure_time",
  "facility_name",
  "building_name",
  "floor_number",
  "room_number",
  "appointment_guest_status",
  "appointment_guest_status_name",
  "guest_full_name", // Add guest_full_name here
];

const create = {
  body: Joi.object().keys({
    first_name: Joi.string().required(),
    last_name: Joi.string().required(),
    email: Joi.string().email().allow("").optional(),
    phone: Joi.string().allow("").optional(),
    organization: Joi.string().allow("").optional(),
    guest_type: Joi.number()
      .integer()
      .external(existsMasterData("patient_guest_guest_type"))
      .required(),
    relationship_type: Joi.number()
      .integer()
      .external(existsMasterData("patient_guest_relation_type"))
      .optional(),
    related_person_name: Joi.string().optional(),
    related_person_contact: Joi.string().optional(),
    relationship_status: Joi.number()
      .integer()
      .external(existsMasterData("patient_guest_relationship_status"))
      .optional(),
    is_emergency_contact: Joi.boolean().optional(),
    emergency_contact_priority: Joi.number().integer().optional(),
    can_make_decisions: Joi.boolean().optional(),
    has_custody: Joi.boolean().optional(),
    lives_with_patient: Joi.boolean().optional(),
    relationship_notes: Joi.string().optional(),
    effective_from: Joi.date().optional(),
    effective_to: Joi.date().optional(),
    reason: Joi.string().when("guest_type", {
      is: 2,
      then: Joi.required(),
      otherwise: Joi.optional(),
    }),
    denied_on: Joi.date().optional(),
    appointment_id: Joi.string()
      .uuid()
      .required()
      .external(exists("Appointment", "appointment_id")),
    start_date: Joi.date().optional(),
    start_time: Joi.string().optional(),
    duration: Joi.number().integer().optional(),
    facility_id: Joi.string().uuid().optional(),
    escort_name: Joi.string().optional(),
    screening: Joi.number().integer().optional(),
    image: Joi.string().allow(null).optional(),
    status: Joi.number().integer().allow(null).optional(),
  }),
};

const denied = {
  body: Joi.object().keys({
    first_name: Joi.string().required(),
    last_name: Joi.string().required(),
    email: Joi.string().email().allow("").optional(),
    phone: Joi.string().allow("").optional(),
    guest_type: Joi.number().integer().valid(0, 1, 2).required(),
    reason: Joi.string().optional(),
    denied_on: Joi.date().optional(),
    patient_id: Joi.string()
      .uuid()
      .required()
      .external(exists("Patient", "patient_id")),
    image: Joi.string().allow(null).optional(),
    birth_date: Joi.date().allow(null).optional(),
    relationship_type: Joi.number()
      .integer()
      .external(existsMasterData("patient_guest_relation_type"))
      .optional(),
    relationship_status: Joi.number()
      .integer()
      .external(existsMasterData("patient_guest_relationship_status"))
      .optional(),
  }),
};

const image = {
  params: Joi.object().keys({
    patient_guest_id: Joi.string().uuid().required(),
  }),
  body: Joi.object().keys({
    image: Joi.string().required(),
  }),
};

const update = {
  params: Joi.object().keys({
    patient_guest_id: Joi.string().uuid().required(),
  }),
  body: Joi.object().keys({
    first_name: Joi.string().optional(),
    last_name: Joi.string().optional(),
    email: Joi.string().email().optional(),
    phone: Joi.string().optional(),
    guest_type: Joi.number().integer().optional(),
    relationship_type: Joi.number().integer().optional(),
    reason: Joi.string().optional(),
    denied_on: Joi.date().optional(),
  }),
};

const updateFriend = {
  params: Joi.object().keys({
    patient_guest_id: Joi.string().uuid().required(),
  }),
  body: Joi.object().keys({
    first_name: Joi.string().optional(),
    last_name: Joi.string().optional(),
    email: Joi.string().email().optional(),
    phone: Joi.string().optional(),
    relationship_type: Joi.number().integer().optional(),
    // Add other fields as needed
  }),
};

const deleteGuest = {
  params: Joi.object().keys({
    patient_guest_id: Joi.string().uuid().required(),
  }),
};

const guestList = {
  query: Joi.object().keys({
    patient_id: Joi.string().uuid().required(),
    search: Joi.string().allow("").optional(),
    appointment_status: Joi.number().integer().valid(0, 1, 2).optional(),
    page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).default(100).optional(),
    sortBy: Joi.string()
      .valid(
        "first_name",
        "guest_full_name",
        "guest_arrival_time",
        "guest_departure_time",
        "facility_name",
        "building_name",
        "floor_number",
        "room_number",
        "appointment_guest_status",
        "appointment_guest_status_name"
      )
      .optional(),
    sortOrder: Joi.string().valid("ASC", "DESC"),
  }),
};

const friends = {
  query: Joi.object().keys({
    patient_id: Joi.string().uuid().required(),
    search: Joi.string().allow("").optional(),
    guest_type: Joi.number().integer().valid(1).optional(),
    page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).default(100).optional(),
    sortBy: Joi.string()
      .valid(...guestAttributes)
      .optional(), // Add valid fields here
    sortOrder: Joi.string().valid("ASC", "DESC"),
  }),
};

const checkGuest = {
  params: Joi.object().keys({
    appointment_guest_id: Joi.string().uuid().required(),
  }),
  query: Joi.object().keys({
    action: Joi.string().valid("checkIn", "checkOut").required(),
    appointment_guest_id: Joi.string().uuid().optional(),
  }),
};
const overrideScreening = {
  params: Joi.object().keys({
    appointment_guest_screening_id: Joi.string().uuid().required(),
  }),
  body: Joi.object().keys({
    reason: Joi.string().optional(),
  }),
};
const getScreeningMatches = {
  params: Joi.object().keys({
    appointment_guest_screening_id: Joi.string().uuid().required(),
  }),
};

const index = {
  query: Joi.object().keys({
    guest_name: Joi.string().optional(),
    guest_type: Joi.number().integer().optional(),
    appointment_id: Joi.string().uuid().optional(),
    patient_id: Joi.string().uuid().optional(),
    appointment_guest_status: Joi.number()
      .integer()
      .valid(0, 1, 2, 3, 4)
      .optional(),
    page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).default(100).optional(),
    sortBy: Joi.string()
      .valid(...patientGuestAttributes)
      .optional(), // Add valid fields here
    sortOrder: Joi.string().valid("ASC", "DESC"), // Only allow ASC or DESC
  }),
};

const search = {
  query: Joi.object().keys({
    search: Joi.string().optional(),
    facility_id: Joi.string().uuid().optional()
  }),
};

const patientGuestHistory = {
  query: Joi.object().keys({
    patient_guest_id: Joi.string().uuid().required(),
  }),
};
const patientAllGuestHistory = {
  page: Joi.number().integer().min(1).optional(),
  limit: Joi.number().integer().min(1).optional(),
  sortBy: Joi.string()
    .valid(...guestHistoryAttributes)
    .optional(),
  sortOrder: Joi.string().valid("ASC", "DESC").optional(),
};

module.exports = {
  create,
  denied, // For creating denied guests
  image,
  update,
  deleteGuest,
  guestList,
  friends,
  checkGuest,
  index,
  search,
  patientGuestHistory,
  patientAllGuestHistory,
  overrideScreening,
  getScreeningMatches,
  updateFriend,
};
