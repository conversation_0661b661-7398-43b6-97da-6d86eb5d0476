const { LanguagePreference } = require("../models");
const { sendSuccess, catchAsync } = require("../helpers/api.helper");
const { status: httpStatus } = require('http-status');

/**
 * @desc    Update user language preference
 * @param   {Object} req - Express request object
 * @param   {Object} req.body - Contains the language_id to set as preference
 * @param   {Object} req.user - Contains the current user's identity_id
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object confirming the update
 */

exports.language = catchAsync(async (req, res) => {
  const { language_id, updated_by } = req.body;
  const { identity_id } = req.identity;

  // Update or create the language preference for the user
  await LanguagePreference.upsert({
    identity_id,
    language_id,
    updated_by,
  });

  sendSuccess(res, "Language preference updated successfully.", httpStatus.OK);
});