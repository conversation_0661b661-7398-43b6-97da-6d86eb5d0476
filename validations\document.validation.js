const Joi = require("joi");
const { existsMasterData } = require("./custom.validation");

module.exports = {
  createDocument: {
    body: Joi.object().keys({
      document_type: Joi.number().integer().external(existsMasterData("document_type")).required(),
      document_number: Joi.string().required(),
      status: Joi.number().integer().external(existsMasterData("document_status")).required(),
      issue_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      expiration_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      country_id: Joi.string().uuid().optional().allow(""),
      state_id: Joi.string().uuid().optional().allow(""),
      other_issuer: Joi.string().optional().allow(""),
      note: Joi.string().optional().allow(""),
      document: Joi.string().optional().allow(""),
      identity_id: Joi.string().uuid().required(),
      created_by: Joi.string().uuid().optional().allow(""),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },

  updateDocument: {
    params: Joi.object().keys({
      document_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      document_type:Joi.number().integer().external(existsMasterData("document_type")).optional(),
      document_number: Joi.string().optional(),
      status:Joi.number().integer().external(existsMasterData("document_status")).optional(),
      issue_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      expiration_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      country_id: Joi.string().uuid().optional().allow(""),
      state_id: Joi.string().uuid().optional().allow(""),
      other_issuer: Joi.string().optional().allow(""),
      note: Joi.string().optional().allow(""),
      document: Joi.string().optional().allow(""),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },

  getDocumentById: {
    params: Joi.object().keys({
      document_id: Joi.string().uuid().required(),
    }),
  },

  deleteDocument: {
    params: Joi.object().keys({
      document_id: Joi.string().uuid().required(),
    }),
  },

  getDocuments: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      search: Joi.string().allow("").optional(),
      identity_id: Joi.string().uuid().optional(),
      document_type: Joi.number().integer().external(existsMasterData("document_type")).optional(),
      status: Joi.number().integer().external(existsMasterData("document_status")).optional(),
    }),
  },
};
