const Joi = require("joi");

module.exports = {
  createVehicle: {
    body: Joi.object().keys({
      plate_number: Joi.string().required(),
      issued_by: Joi.string().optional().allow(""),
      vin: Joi.string().length(17).optional().allow(""),
      year: Joi.number().integer().min(1900).max(new Date().getFullYear() + 1).optional(),
      make: Joi.string().optional().allow(""),
      model: Joi.string().optional().allow(""),
      color: Joi.string().optional().allow(""),
      uploaded_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      identity_id: Joi.string().uuid().required(),
      created_by: Joi.string().uuid().optional().allow(""),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },

  updateVehicle: {
    params: Joi.object().keys({
      vehicle_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      plate_number: Joi.string().optional(),
      issued_by: Joi.string().optional().allow(""),
      vin: Joi.string().optional().allow(""),
      year: Joi.number().integer().min(1900).max(new Date().getFullYear() + 1).optional(),
      make: Joi.string().optional().allow(""),
      model: Joi.string().optional().allow(""),
      color: Joi.string().optional().allow(""),
      uploaded_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      updated_by: Joi.string().uuid().optional().allow(""),
    }),
  },

  getVehicleById: {
    params: Joi.object().keys({
      vehicle_id: Joi.string().uuid().required(),
    }),
  },

  deleteVehicle: {
    params: Joi.object().keys({
      vehicle_id: Joi.string().uuid().required(),
    }),
  },

  getVehicles: {
    query: Joi.object().keys({
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
      search: Joi.string().allow("").optional(),
      identity_id: Joi.string().uuid().optional(),
      make: Joi.string().optional(),
      model: Joi.string().optional(),
      year: Joi.number().integer().optional(),
    }),
  },

};
