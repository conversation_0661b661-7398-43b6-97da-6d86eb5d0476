const express = require('express');
const passport = require('../config/passport');
const router = express.Router();

// -------------
// SAML Routes
// -------------
router.get('/saml', passport.authenticate('saml'));

router.post(
  '/saml/callback',
  passport.authenticate('saml', { failureRedirect: '/login', session: false }),
  (req, res) => {
    // On success, you can generate a JWT or set a session as needed.
    res.redirect('/');
  }
);

// -------------
// OpenID Connect Routes
// -------------
router.get('/openid', passport.authenticate('openidconnect'));

router.get(
  '/openid/callback',
  passport.authenticate('openidconnect', { failureRedirect: '/login', session: false }),
  (req, res) => {
    res.redirect('/');
  }
);

// -------------
// Azure AD Routes
// -------------
router.get(
  '/azure',
  passport.authenticate('azuread-openidconnect', { failureRedirect: '/login', session: false })
);

router.post(
  '/azure/callback',
  passport.authenticate('azuread-openidconnect', { failureRedirect: '/login', session: false }),
  (req, res) => {
    res.redirect('/');
  }
);

module.exports = router;
