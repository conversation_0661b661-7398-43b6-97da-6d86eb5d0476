const express = require("express");
const validate = require("../middlewares/validate");
const AppointmentValidation = require("../validations/appointment.validation");
const AppointmentController = require("../controllers/appointment.controller");
const auth = require("../middlewares/auth");
const patientGuestRoutes = require("./patientGuest.route");
const hipaaLogger = require("../middlewares/hipaaLogger");
const uploadToBase64 = require("../middlewares/upload");


const router = express.Router({ mergeParams: true });

/**
 * @swagger
 * tags:
 *   name: Appointments
 *   description: Appointment management
 */

/**
 * @swagger
 * /appointments/search:
 *   get:
 *     summary: Search and retrieve today's patient appointments
 *     description: >
 *       Retrieves appointments scheduled for the current date, optionally filtered by
 *       appointment type and a search term (matches patient name, mrn, or preferred_name).
 *       Returns only four fields per appointment: appointment_id, appointment_date,
 *       patient_name (first + last), and image.
 *     tags:
 *       - Appointments
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Free-text search term to match against patient name, mrn, or preferred_name.
 *       - in: query
 *         name: type
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Appointment type key (matches view.type).
 *       - in: query
 *         name: facility_id
 *         schema:
 *           type: string
 *         description: The facility Id
 *     responses:
 *       200:
 *         description: A list of today's appointments.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Today's appointments retrieved successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       appointment_id:
 *                         type: string
 *                         format: uuid
 *                         example: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
 *                       appointment_date:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-04-15T10:30:00.000Z"
 *                       patient_name:
 *                         type: string
 *                         example: "Jane Doe"
 *                       image:
 *                         type: string
 *                         description: URL or media identifier for the patient's image
 *                         example: "https://cdn.example.com/patients/3fa85f64-.../avatar.jpg"
 *       400:
 *         description: Invalid query parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         description: Unauthorized (missing or invalid token / insufficient permissions)
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get(
  "/search",
  auth("search_appointments"), 
  validate(AppointmentValidation.search),
  hipaaLogger,
  AppointmentController.search,

);

/**
 * @swagger
 * /appointments/{appointment_id}:
 *   get:
 *     summary: Get appointment by ID
 *     description: |
 *       Retrieves full details of a single appointment by its UUID.
 *       Excludes the following fields from the response:
 *       - patient_id
 *       - type
 *       - updated_by
 *       - created_at
 *       - updated_at
 *     tags:
 *       - Appointments
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: appointment_id
 *         required: true
 *         schema:
 *           type: string
 *         description: The appointmnet Id
 *     responses:
 *       200:
 *         description: Appointment retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Appointment retrieved successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     appointment_id:
 *                       type: string
 *                       format: uuid
 *                     appointment_date:
 *                       type: string
 *                       format: date-time
 *                     department:
 *                       type: string
 *                     provider_name:
 *                       type: string
 *                     status:
 *                       type: integer
 *                     appointment_status_name:
 *                       type: string
 *                     appointment_type_name:
 *                       type: string
 *                     facility_id:
 *                       type: string
 *                       format: uuid
 *                     room_number:
 *                       type: integer
 *                     confidentiality_code:
 *                       type: integer
 *                     beds:
 *                       type: string
 *                     first_name:
 *                       type: string
 *                     last_name:
 *                       type: string
 *                     birth_date:
 *                       type: string
 *                       format: date
 *                     patient_gender:
 *                       type: integer
 *                     image:
 *                       type: string
 *                       description: URL or media identifier for the patient's image
 */
router.get(
  "/:appointment_id",
  auth("view_appointments"),
  validate(AppointmentValidation.show),
  hipaaLogger,
  AppointmentController.show
);
/**
 * @swagger
 * /appointments/patient/{patient_id}:
 *   get:
 *     summary: Get outpatient appointments by patient ID
 *     description: >
 *       Retrieves outpatient appointments for a specific patient for the current date.
 *       Supports pagination, sorting, and search functionality.
 *     tags:
 *       - Appointments
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the patient
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Free-text search term to match against patient name
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by (default is "appointment_date")
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is "ASC")
 *     responses:
 *       200:
 *         description: Outpatient appointments retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Outpatient appointments retrieved successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     currentPage:
 *                       type: integer
 *                     data:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           patient_first_name:
 *                             type: string
 *                           patient_last_name:
 *                             type: string
 *                           appointment_date:
 *                             type: string
 *                             format: date-time
 *                           location:
 *                             type: string
 *                           guest_first_name:
 *                             type: string
 *                           guest_last_name:
 *                             type: string
 *       404:
 *         description: No outpatient appointments found
 *       500:
 *         description: Internal server error
 */
router.get(
  "/patient/:patient_id",
  auth("view_appointments"),
  validate(AppointmentValidation.outpatient),
  hipaaLogger,
  AppointmentController.outpatient
);

/**
 * @swagger
 * /appointments/patient/{patient_id}/future:
 *   get:
 *     summary: Get future appointments by patient ID
 *     description: >
 *       Retrieves all future appointments for a specific patient where the appointment date is greater than the current date.
 *       Supports pagination, sorting, and search functionality.
 *     tags:
 *       - Appointments
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: patient_id
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the patient
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Free-text search term to match against patient name
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10)
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by (default is "appointment_date")
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *         description: Sort order (default is "ASC")
 *     responses:
 *       200:
 *         description: Future appointments retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Future appointments retrieved successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *                     currentPage:
 *                       type: integer
 *                     data:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           patient_first_name:
 *                             type: string
 *                           patient_last_name:
 *                             type: string
 *                           appointment_date:
 *                             type: string
 *                             format: date-time
 *                           location:
 *                             type: string
 *                           guest_first_name:
 *                             type: string
 *                           guest_last_name:
 *                             type: string
 *       404:
 *         description: No future appointments found
 *       500:
 *         description: Internal server error
 */
router.get(
  "/patient/:patient_id/future",
  auth("view_appointments"),
  validate(AppointmentValidation.futureAppointments),
  hipaaLogger,
  AppointmentController.future
);

/**
 * @swagger
 * /appointments/{appointment_id}/check:
 *   patch:
 *     summary: Check-in or check-out a patient
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: appointment_id
 *         in: path
 *         required: true
 *         description: The ID of appointment to check in or check out.
 *         schema:
 *           type: string
 *           format: uuid
 *       - name: action
 *         in: query
 *         required: true
 *         description: The action to perform ("checkIn" or "checkOut").
 *         schema:
 *           type: string
 *           enum: [checkIn, checkOut]
 *     responses:
 *       200:
 *         $ref: '#/components/responses/PatientSuccess'
 *       400:
 *         description: Invalid action specified
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       404:
 *         $ref: '#/components/responses/NotFound'
 */
router.patch(
  "/:appointment_id/check",
  auth("view_appointments"),
  validate(AppointmentValidation.checkPatient), 
  hipaaLogger,
  AppointmentController.checkPatient
);

/**
 * @swagger
 * /appointments/{patient_id}/image:
 *   patch:
 *     summary: Update patient image
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PatientIdPath'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *     responses:
 *       200:
 *         $ref: '#/components/responses/PatientSuccess'
 */

// Then outside of any path:
/**
 * @swagger
 * /appointments/{patient_id}/image:
 *   patch:
 *     summary: Upload or update a patient image
 *     tags: [Appointments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PatientIdPath'
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               image:
 *                 type: string
 *                 format: binary
 *                 description: Image file to upload
 *     responses:
 *       200:
 *         $ref: '#/components/responses/PatientSuccess'
 *       400:
 *         description: Invalid input
 *       404:
 *         description: Patient not found
 */


router.patch(
  "/:patient_id/image",
  auth("view_appointments"),
  uploadToBase64("image"),
  validate(AppointmentValidation.image),
  hipaaLogger,
  AppointmentController.image
);

router.use('/guests', patientGuestRoutes); 
module.exports = router;
