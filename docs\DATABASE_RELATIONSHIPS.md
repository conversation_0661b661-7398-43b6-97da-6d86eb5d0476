# CareMate API Database Relationships Documentation

## Overview

This document provides a comprehensive overview of the CareMate API database schema, focusing on the relationships between Device, NDA, Signature, Kiosk, and all related entities. The system is designed to manage healthcare facilities, patient appointments, guest management, access control, and digital signature workflows.

## Core Entity Groups

### 1. Infrastructure & Location Management
- **Facility**: Healthcare facilities (hospitals, clinics)
- **Address**: Physical addresses for facilities
- **Building**: Buildings within facilities
- **Floor**: Floors within buildings
- **Room**: Rooms within floors
- **Country/State**: Geographic location data
- **Timezone**: Time zone management

### 2. Device & Kiosk Management
- **Device**: Physical kiosk devices
- **KioskGroup**: Logical grouping of devices
- **DeviceSetting**: Device-specific configurations
- **KioskSetting**: Global kiosk configuration options
- **KioskGroupSetting**: Group-specific configurations

### 3. NDA & Digital Signature System
- **NdaTemplate**: NDA document templates
- **NdaAgreement**: Individual NDA agreements
- **NdaSignature**: Digital signatures on NDAs
- **AppointmentGuestNdaAgreement**: Links guests to NDA agreements

### 4. Identity & Access Management
- **Identity**: Users (staff, patients, guests)
- **IdentityVerification**: Authentication credentials
- **Role**: User roles
- **Permission**: System permissions
- **AccessLevel**: Physical access levels
- **Card**: Access cards
- **System**: External systems (PACS)

### 5. Patient & Appointment Management
- **Patient**: Patient records
- **PatientAddress**: Patient addresses
- **PatientIdentifier**: Patient identifiers (MRN)
- **Appointment**: Medical appointments
- **PatientGuest**: Guests associated with patients
- **AppointmentGuest**: Guests attending appointments

### 6. Visitor Management
- **Guest**: General visitors
- **Visit**: Scheduled visits
- **GuestVisit**: Junction table for guest-visit relationships

### 7. Master Data
- **MasterData**: Lookup values for dropdowns and categorization
- **Function**: Functional categorization

## Key Relationships

### Device Ecosystem

#### Device Location Hierarchy
```
Facility (1) -> (0..n) Device
Building (1) -> (0..n) Device  
Floor (1) -> (0..n) Device
Room (1) -> (0..n) Device
```

#### Device Configuration
```
Device (1) -> (0..n) DeviceSetting
DeviceSetting (n) -> (1) NdaTemplate
KioskGroup (1) -> (0..n) Device
KioskGroup (1) -> (0..n) KioskGroupSetting
KioskGroupSetting (n) -> (1) KioskSetting
```

### NDA & Signature Workflow

#### NDA Template to Agreement
```
NdaTemplate (1) -> (0..n) NdaAgreement
NdaTemplate (1) -> (0..n) DeviceSetting
```

#### NDA Agreement Lifecycle
```
Identity (1) -> (0..n) NdaAgreement [signer]
NdaAgreement (1) -> (0..n) NdaSignature
NdaSignature (n) -> (1) Identity [signer]
```

#### Guest NDA Integration
```
AppointmentGuest (1) -> (0..n) AppointmentGuestNdaAgreement
AppointmentGuestNdaAgreement (n) -> (1) NdaAgreement
```

### Patient & Guest Management

#### Patient Hierarchy
```
Patient (1) -> (0..n) Appointment
Patient (1) -> (0..n) PatientGuest
Patient (1) -> (0..1) PatientAddress
Patient (1) -> (0..1) PatientIdentifier
```

#### Appointment Guest Flow
```
Appointment (1) -> (0..n) AppointmentGuest
PatientGuest (1) -> (0..n) AppointmentGuest
AppointmentGuest (1) -> (0..n) AppointmentGuestCheckin
```

### Access Control System

#### Identity Access Management
```
Identity (1) -> (0..n) IdentityAccess
IdentityAccess (n) -> (1) AccessLevel
AccessLevel (n) -> (0..1) System
AccessLevel (n) -> (0..1) Card
```

#### Role-Based Permissions
```
Identity (n) -> (n) Role [via IdentityRole]
Role (n) -> (n) Permission [via RolePermission]
```

## Foreign Key Relationships

### Device Related
- `device.kiosk_group_id` -> `kiosk_group.kiosk_group_id` (CASCADE)
- `device.facility_id` -> `facility.facility_id` (SET NULL)
- `device.facility_building_id` -> `building.building_id` (SET NULL)
- `device.facility_floor_id` -> `floor.floor_id` (SET NULL)
- `device.facility_room_id` -> `room.room_id` (SET NULL)

### Device Settings
- `device_setting.device_id` -> `device.device_id` (CASCADE)
- `device_setting.nda_template_id` -> `nda_template.nda_template_id` (CASCADE)

### NDA System
- `nda_agreement.identity_id` -> `identity.identity_id` (CASCADE)
- `nda_agreement.nda_template_id` -> `nda_template.nda_template_id` (CASCADE)
- `nda_signature.nda_agreement_id` -> `nda_agreement.nda_agreement_id` (CASCADE)
- `nda_signature.signer_id` -> `identity.identity_id` (CASCADE)

### Appointment Guest NDA
- `appointment_guest_nda_agreement.appointment_guest_id` -> `appointment_guest.appointment_guest_id` (CASCADE)
- `appointment_guest_nda_agreement.nda_agreement_id` -> `nda_agreement.nda_agreement_id` (CASCADE)

### Facility Structure
- `address.facility_id` -> `facility.facility_id` (CASCADE)
- `building.facility_id` -> `facility.facility_id` (CASCADE)
- `floor.facility_id` -> `facility.facility_id` (CASCADE)
- `floor.building_id` -> `building.building_id` (CASCADE)
- `room.facility_id` -> `facility.facility_id` (CASCADE)
- `room.building_id` -> `building.building_id` (CASCADE)
- `room.floor_id` -> `floor.floor_id` (SET NULL)

### Patient Management
- `appointment.patient_id` -> `patient.patient_id` (CASCADE)
- `appointment.facility_id` -> `facility.facility_id` (CASCADE)
- `patient_guest.patient_id` -> `patient.patient_id` (CASCADE)
- `appointment_guest.appointment_id` -> `appointment.appointment_id` (CASCADE)
- `appointment_guest.patient_guest_id` -> `patient_guest.patient_guest_id` (CASCADE)

### Visitor Management
- `visit.facility_id` -> `facility.facility_id` (CASCADE)
- `visit.host_id` -> `identity.identity_id` (CASCADE)
- `visit.escort_id` -> `identity.identity_id` (SET NULL)
- `guest_visit.guest_id` -> `guest.guest_id` (CASCADE)
- `guest_visit.visit_id` -> `visit.visit_id` (CASCADE)

## Master Data Integration

The system uses a centralized `master_data` table for categorization and lookup values. Key groups include:

### Device & Kiosk Related
- `device_status`
- `kiosk_group_type`

### NDA Related
- `nda_agreement_status`
- `signer_role`
- `signature_method`

### Facility Related
- `facility_status`
- `facility_type`
- `building_status`
- `building_type`
- `building_occupancy_type`
- `floor_status`
- `floor_occupancy_type`
- `room_status`

### Identity & Access
- `identity_status`
- `identity_type`
- `access_level_status`
- `access_level_type`
- `card_type`

### Patient & Guest
- `patient_gender`
- `patient_marital_status`
- `patient_confidentiality_code`
- `appointment_status`
- `appointment_type`
- `guest_type`
- `relationship_type`
- `guest_status`

## Unique Constraints

### Business Logic Constraints
- `nda_template`: Unique combination of `name` + `version`
- `appointment_guest`: Unique combination of `appointment_id` + `patient_guest_id`
- `appointment_guest_nda_agreement`: Unique combination of `appointment_guest_id` + `nda_agreement_id`
- `guest_visit`: Unique combination of `guest_id` + `visit_id`
- `master_data`: Unique combination of `group` + `key`

### Identity Constraints
- `identity.email`: Unique across all identities
- `device.identifier`: Unique device identifiers
- `facility.facility_code`: Unique facility codes
- `building.building_code`: Unique building codes

## Soft Delete Support

The following tables support soft deletes (paranoid mode):
- `master_data`: Uses `deleted_at` timestamp

## Audit Trail

All major entities include audit fields:
- `created_at`: Record creation timestamp
- `updated_at`: Last modification timestamp
- `updated_by`: UUID of user who made the change

## Indexes

### Performance Indexes
- Device lookups: `device.identifier`, `device.facility_id`
- Guest management: `guest.email`, `guest_visit.guest_id`, `guest_visit.visit_id`
- Appointment queries: `appointment.patient_id`, `appointment.facility_id`
- Identity searches: `identity.email`, `identity.facility_id`
- Access control: `identity_access.identity_id`, `identity_access.access_level_id`

### Unique Indexes
- Business constraints as listed above
- Foreign key relationships for referential integrity

## Data Flow Examples

### Device Check-in with NDA
1. Device authenticates via `device.identifier`
2. Device settings loaded via `device_setting.device_id`
3. NDA template retrieved via `device_setting.nda_template_id`
4. Guest creates `appointment_guest` record
5. NDA agreement created via `nda_agreement`
6. Digital signature captured via `nda_signature`
7. Link created via `appointment_guest_nda_agreement`

### Patient Guest Registration
1. Patient lookup via `patient.patient_id`
2. Guest created via `patient_guest`
3. Appointment association via `appointment_guest`
4. Check-in process via `appointment_guest_checkin`
5. NDA workflow if required

## Model Associations Summary

### Device Model Associations
```javascript
Device.belongsTo(KioskGroup, { foreignKey: "kiosk_group_id", as: "kiosk_group" })
Device.belongsTo(Facility, { foreignKey: "facility_id", as: "facility" })
Device.belongsTo(Building, { foreignKey: "facility_building_id", as: "building" })
Device.belongsTo(Floor, { foreignKey: "facility_floor_id", as: "floor" })
Device.belongsTo(Room, { foreignKey: "facility_room_id", as: "room" })
Device.hasMany(DeviceSetting, { foreignKey: "device_id", as: "settings" })
```

### NDA Model Associations
```javascript
NdaTemplate.hasMany(NdaAgreement, { foreignKey: "nda_template_id", as: "agreements" })
NdaTemplate.hasMany(DeviceSetting, { foreignKey: "nda_template_id", as: "device_settings" })

NdaAgreement.belongsTo(NdaTemplate, { foreignKey: "nda_template_id", as: "template" })
NdaAgreement.belongsTo(Identity, { foreignKey: "identity_id", as: "identity" })
NdaAgreement.hasMany(NdaSignature, { foreignKey: "nda_agreement_id", as: "signatures" })

NdaSignature.belongsTo(NdaAgreement, { foreignKey: "nda_agreement_id", as: "agreement" })
NdaSignature.belongsTo(Identity, { foreignKey: "signer_id", as: "identity" })
```

### Kiosk Model Associations
```javascript
KioskGroup.hasMany(Device, { foreignKey: "kiosk_group_id", as: "devices" })
KioskGroup.hasMany(KioskGroupSetting, { foreignKey: "kiosk_group_id", as: "settings" })

KioskSetting.hasMany(KioskGroupSetting, { foreignKey: "kiosk_setting_id", as: "group_settings" })
KioskSetting.belongsTo(MasterData, { foreignKey: "masterdata_id", as: "masterdata" })

KioskGroupSetting.belongsTo(KioskGroup, { foreignKey: "kiosk_group_id", as: "kiosk_group" })
KioskGroupSetting.belongsTo(KioskSetting, { foreignKey: "kiosk_setting_id", as: "kiosk_setting" })
```

### Patient & Guest Model Associations
```javascript
Patient.hasMany(Appointment, { foreignKey: "patient_id", as: "appointments" })
Patient.hasMany(PatientGuest, { foreignKey: "patient_id", as: "guests" })
Patient.hasOne(PatientIdentifier, { foreignKey: "patient_id", as: "patientIdentifier" })
Patient.hasOne(PatientAddress, { foreignKey: "patient_id", as: "patient_address" })

PatientGuest.belongsTo(Patient, { foreignKey: "patient_id", as: "patient" })
PatientGuest.hasMany(AppointmentGuest, { foreignKey: "patient_guest_id", as: "appointment_guests" })

AppointmentGuest.belongsTo(Appointment, { foreignKey: "appointment_id", as: "appointment" })
AppointmentGuest.belongsTo(PatientGuest, { foreignKey: "patient_guest_id", as: "patientGuest" })
AppointmentGuest.hasMany(AppointmentGuestNdaAgreement, { foreignKey: "appointment_guest_id", as: "nda_agreements" })
```

### Visitor Management Associations
```javascript
Guest.belongsToMany(Visit, { through: GuestVisit, foreignKey: "guest_id", as: "visits" })
Guest.hasMany(GuestVisit, { foreignKey: "guest_id", as: "guestVisits" })

Visit.belongsToMany(Guest, { through: GuestVisit, foreignKey: "visit_id", as: "guests" })
Visit.hasMany(GuestVisit, { foreignKey: "visit_id", as: "guestVisits" })
Visit.belongsTo(Facility, { foreignKey: "facility_id", as: "facility" })
Visit.belongsTo(Identity, { foreignKey: "host_id", as: "host" })
Visit.belongsTo(Identity, { foreignKey: "escort_id", as: "escort" })

GuestVisit.belongsTo(Guest, { foreignKey: "guest_id", as: "guest" })
GuestVisit.belongsTo(Visit, { foreignKey: "visit_id", as: "visit" })
```

## API Integration Points

### Kiosk Controller Usage
The kiosk controller demonstrates key integration patterns:

1. **Device Authentication**: `getDeviceSetting` validates device via `device.identifier`
2. **Patient Lookup**: Uses `PatientAppointmentView` for optimized queries
3. **Guest Management**: Creates `PatientGuest` and `AppointmentGuest` records
4. **NDA Integration**: Links guests to NDA agreements via `AppointmentGuestNdaAgreement`

### Common Query Patterns
```javascript
// Device with all location details
Device.findOne({
  include: [
    { model: Facility, as: "facility" },
    { model: Building, as: "building" },
    { model: Floor, as: "floor" },
    { model: Room, as: "room" },
    { model: DeviceSetting, as: "settings", include: [
      { model: NdaTemplate, as: "nda_template" }
    ]}
  ]
})

// Patient with appointments and guests
Patient.findOne({
  include: [
    { model: Appointment, as: "appointments" },
    { model: PatientGuest, as: "guests", include: [
      { model: AppointmentGuest, as: "appointment_guests" }
    ]}
  ]
})
```

## Database Views

The system includes several database views for optimized queries:

### PatientAppointmentView
Combines patient and appointment data for efficient lookups in kiosk operations.

### IdentityAccessView
Provides flattened view of identity access permissions for authorization checks.

### GuestScreeningView
Optimized view for guest screening and check-in processes.

## Security Considerations

### Data Protection
- All sensitive fields use UUID primary keys
- Soft deletes preserve audit trails
- `updated_by` fields track data modifications
- Password hashes stored separately in `identity_verification`

### Access Control
- Role-based permissions via `role_permission` junction
- Facility-scoped access via `identity.facility_id`
- Time-bound access via `effective_from`/`effective_to` dates
- Card-based physical access integration

### NDA Compliance
- Digital signatures with timestamp and method tracking
- Immutable signature records (no updates, only inserts)
- Template versioning for legal compliance
- Audit trail for all NDA-related actions

## Performance Optimization

### Indexing Strategy
- Composite indexes on frequently queried combinations
- Foreign key indexes for join performance
- Unique constraints for data integrity
- Partial indexes on active records

### Query Optimization
- Database views for complex joins
- Pagination support via `paginate` plugin
- Selective field loading in API responses
- Efficient relationship loading patterns

This documentation provides the foundation for understanding the complex relationships within the CareMate API database schema, with particular focus on the device, NDA, signature, and kiosk management systems.
