const Joi = require("joi");
const { getModelAttributes } = require("../helpers/global.helper");
const {PatientGuest , PatientAppointmentView , PatientHistoryView ,Hl7Message} = require("../models");

const hl7Attributes = getModelAttributes(Hl7Message)
const guestAttributes = getModelAttributes(PatientGuest);
const patientHistoryAttributes = getModelAttributes(PatientHistoryView);
const patientAppointmentAttributes = getModelAttributes(PatientAppointmentView);

const getPatientAppointments = {
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).default(10).optional(),
    search: Joi.string().optional(),
    status: Joi.number().integer().optional(),
    page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).default(100).optional(),
    sortBy: Joi.string().valid(...patientAppointmentAttributes).optional(),
    sortOrder: Joi.string().valid("ASC", "DESC"),
  }),
};

const getPatientHistory = {
  query: Joi.object().keys({
    patient_id: Joi.string().uuid().required(),
  }),
};
const getAllPatientHistory = {
  page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).default(100).optional(),
    sortBy: Joi.string().valid(...patientHistoryAttributes).optional(),
    sortOrder: Joi.string().valid("ASC", "DESC"),
};

const getPatientInformation = {
  query: Joi.object().keys({
    patient_id: Joi.string().uuid().required(),
  }),
};

const getPatientDetails = {
  query: Joi.object().keys({
    patient_id: Joi.string().uuid().required(),
  }),
};

const updatePatientDetails = {
  params: Joi.object().keys({
    patient_id: Joi.string().uuid().required(),
  }),
  body: Joi.object().keys({
    first_name: Joi.string().optional(),
    last_name: Joi.string().optional(),
    middle_name: Joi.string().optional(),
    email: Joi.string().email().optional(),
    phone: Joi.string().optional(),
    preferred_name: Joi.string().optional(),
    birth_date: Joi.date().optional(),
    updated_by: Joi.string().uuid().optional(),
  }),
};

const updateAdmissionDetails = {
  params: Joi.object().keys({
    patient_id: Joi.string().uuid().required(),
  }),
  body: Joi.object().keys({
    type: Joi.number().integer().optional(),
    arrival_time: Joi.date().optional(),
    discharge_time: Joi.date().optional(),
    death_date: Joi.date().optional(),
    confidentiality_code: Joi.number().integer().optional(),
  }),
};

const updateFacilityDetails = {
  params: Joi.object().keys({
    patient_id: Joi.string().uuid().required(),
  }),
  body: Joi.object().keys({
    facility_id: Joi.string().uuid().required(),
    facility_name: Joi.string().optional(),
    building_id: Joi.string().uuid().optional(),
    building_name: Joi.string().optional(),
    floor_id: Joi.string().uuid().optional(),
    floor_number: Joi.number().integer().optional(),
    room_id: Joi.string().uuid().optional(),
    room_number: Joi.string().optional(),
    beds: Joi.number().integer().optional(),
  }),
};

const updateAddress = {
  params: Joi.object().keys({
    patient_id: Joi.string().uuid().required(),
  }),
  body: Joi.object().keys({
    address_line_1: Joi.string().optional(),
    address_line_2: Joi.string().optional(),
    country: Joi.string().optional(),
    state: Joi.string().optional(),
    city: Joi.string().optional(),
    postal_code: Joi.string().optional(),
  }),
};

const updateImage = {
  params: Joi.object().keys({
    patient_id: Joi.string().uuid().required(),
  }),
  body: Joi.object().keys({
    image: Joi.string().required(),
  }),
};

const denied = {
  query: Joi.object().keys({
    patient_id: Joi.string().uuid().required(),
    search: Joi.string().allow("").optional(),
    guest_type: Joi.number().integer().optional(),
    page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).default(100).optional(),
    sortBy: Joi.string()
      .valid(...guestAttributes)
      .optional(),
    sortOrder: Joi.string().valid("ASC", "DESC"),
  }),
};

const searchPatients = {
  query: Joi.object().keys({
    search: Joi.string().optional(),
    type: Joi.number().integer().valid(0, 1).optional().description("Appointment type filter: 0 for inpatient, 1 for outpatient"),
  }),
};

const getHl7Messages = {
  params: Joi.object().keys({
    mrn: Joi.string().required().description('Patient MRN'),
  }),
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).default(1).optional(),
    limit: Joi.number().integer().min(1).default(10).optional(),
    search: Joi.string().allow("").optional(),
     sortBy: Joi.string().valid(...hl7Attributes).optional(),
    sortOrder: Joi.string().valid('ASC', 'DESC').optional(),
  }),
};

module.exports = {
  getPatientAppointments,
  getPatientHistory,
  getAllPatientHistory,
  getPatientInformation,
  updatePatientDetails,
  updateAdmissionDetails,
  updateFacilityDetails,
  updateAddress,
  updateImage,
  getPatientDetails,
  denied,
  searchPatients,
  getHl7Messages,
};
