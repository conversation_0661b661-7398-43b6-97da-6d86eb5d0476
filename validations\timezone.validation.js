const Joi = require("joi");
const { exists } = require("./custom.validation");

const timeId = Joi.string().required().external(exists("Timezone", "time_id"));
const countryId = Joi.string().required().external(exists("Country", "country_id"));

const TimezoneValidation = {
  create: {
    body: Joi.object().keys({
      country_id: countryId,
      code: Joi.string().required(),
    }),
  },
  timezone: {
    params: Joi.object().keys({
      timeId,
    }),
  },
  update: {
    params: Joi.object().keys({
      timeId,
    }),
    body: Joi.object().keys({
      code: Joi.string().optional(),
    }),
  },
};

module.exports = TimezoneValidation;