
const Joi = require("joi");
const { exists, unique } = require("./custom.validation");

// Declare these only once, at the top
const facilityId = Joi.string().required().external(exists("Facility", "facility_id"));
const facility_access_level_id = Joi.string().required().external(exists("FacilityAccessLevel", "facility_access_level_id"));

const remove = {
  params: Joi.object().keys({
    facilityId,
    facility_access_level_id,
  }),
};

const access_level_id = Joi.string().optional().external(unique("FacilityAccessLevel", "access_level_id"));
const building_id = Joi.string().optional().external(exists("Building", "building_id"));
const floor_id = Joi.string().optional().external(exists("Floor", "floor_id"));
const room_id = Joi.string().optional().external(exists("Room", "room_id"));
const requestable_guest = Joi.boolean().optional();
const default_access_guest = Joi.boolean().optional();
const default_access_identity = Joi.boolean().optional();
const identity_type = Joi.array().items(Joi.string().valid("COS", "EMP")).optional();

const facility = {
  params: Joi.object().keys({
    facilityId,
  }),
};

const create = {
  params: Joi.object().keys({
    facilityId,
  }),
  body: Joi.object().keys({
    access_level_id:Joi.string().required().external(unique("FacilityAccessLevel", "access_level_id")),
    building_id,
    floor_id,
    room_id,
    entry_restrictions: Joi.string().optional(),
    access_protocol: Joi.string().optional(),
    requestable_guest,
    default_access_guest,
    default_access_identity,
    identity_type,
  }),
};

const facilityAccessLevel = {
  params: Joi.object().keys({
    facility_access_level_id,
  }),
};

const update = {
  params: Joi.object().keys({
    facilityId,
    facility_access_level_id,
  }),
  body: Joi.object()
    .keys({
      access_level_id,
      building_id,
      floor_id,
      room_id,
      entry_restrictions: Joi.string().optional(),
      access_protocol: Joi.string().optional(),
      requestable_guest,
      default_access_guest,
      default_access_identity,
      identity_type,
    })
    .min(1),
};

module.exports = {
  create,
  facility,
  facilityAccessLevel,
  update,
  remove,
};