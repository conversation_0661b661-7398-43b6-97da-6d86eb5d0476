const Joi = require("joi");
const { exists } = require("./custom.validation");
const { OutpatientGuestFutureView} = require("../models");
const { getModelAttributes } = require("../helpers/global.helper");

// const appointmentAttributes = getModelAttributes(Appointment);
const outpatientAttributes = getModelAttributes(OutpatientGuestFutureView);

module.exports = {
  search: {
    query: Joi.object().keys({
      search: Joi.string().required(),
      type: Joi.number().integer().optional(),
      facility_id: Joi.string().optional() .external(exists("Facility", "facility_id")),
      
    }),
  },
  show: {
    params: Joi.object().keys({
      appointment_id: Joi.string()
        .required()
        .external(exists("Appointment", "appointment_id")),
    }),
  },
  image: {
    params: Joi.object().keys({
      patient_id: Joi.string().uuid().required(),
    }),
    body: Joi.object().keys({
      image: Joi.string().required(),
    }),
  },
  outpatient: {
    params: Joi.object().keys({
      patient_id: Joi.string().uuid().required(),
    }),
    query: Joi.object().keys({
      search: Joi.string().optional(),
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().valid(...outpatientAttributes).optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
    }),
  },
  futureAppointments: {
    params: Joi.object().keys({
      patient_id: Joi.string().uuid().required(),
    }),
    query: Joi.object().keys({
      search: Joi.string().optional(),
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).optional(),
      sortBy: Joi.string().valid(...outpatientAttributes).optional(),
      sortOrder: Joi.string().valid("ASC", "DESC").optional(),
    }),
  },
  checkPatient: {
    params: Joi.object().keys({
      appointment_id: Joi.string().uuid().required(),
    }),
    query: Joi.object().keys({
      action: Joi.string().valid("checkIn", "checkOut").required(),
      appointment_id: Joi.string().uuid().optional(),
    }),
  },
};
