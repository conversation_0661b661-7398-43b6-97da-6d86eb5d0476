const express = require("express");
const validate = require("../middlewares/validate");
const CountryValidation = require("../validations/country.validation");
const CountryController = require("../controllers/country.controller");
const auth = require("../middlewares/auth");

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Countries
 *   description: Country management
 */

router
  .route("/")
  /**
   * @swagger
   * /countries:
   *   get:
   *     summary: Get all countries
   *     tags: [Countries]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: List of countries
   */
  .get(auth("view_countries"), CountryController.index)
  /**
   * @swagger
   * /countries:
   *   post:
   *     summary: Create a new country
   *     tags: [Countries]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 description: Name of the country
   *                 example: United States
   *     responses:
   *       201:
   *         description: Country created
   */
  .post(auth("create_country"), validate(CountryValidation.create), CountryController.create);

router
  .route("/:countryId")
  /**
   * @swagger
   * /countries/{countryId}:
   *   get:
   *     summary: Get a country by ID
   *     tags: [Countries]
   *     parameters:
   *       - name: countryId
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *         description: The ID of the country
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Country details
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/Country'
   */
  .get(auth("view_country"), validate(CountryValidation.country), CountryController.show)
  /**
   * @swagger
   * /countries/{countryId}:
   *   patch:
   *     summary: Update a country
   *     tags: [Countries]
   *     parameters:
   *       - name: countryId
   *         in: path
   *         required: true
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Country updated
   */
  .patch(auth("edit_country"), validate(CountryValidation.update), CountryController.update)
  /**
   * @swagger
   * /countries/{countryId}:
   *   delete:
   *     summary: Delete a country
   *     tags: [Countries]
   *     parameters:
   *       - name: countryId
   *         in: path
   *         required: true
   *     responses:
   *       204:
   *         description: Country deleted
   */
  .delete(auth("delete_country"), validate(CountryValidation.country), CountryController.delete);

module.exports = router;