

const express = require("express");
const healthRoute = require("./health.route");
const authRoute = require("./auth.route");
const authExternalRoute = require("./authExternal.route");
const facilityRoute = require("./facility.route");
const watchlistRoute = require("./watchlist.route");
const systemRoute = require("./system.route");
const buildingRoute = require("./building.routes");
const floorRoute = require("./floor.route");
const roomRoute = require('./room.route');
const countryRoute = require('./country.route');
const stateRoute = require('./state.route');
const timezoneRoute = require('./timezone.route');
const accessLevelRoute = require('./accessLevel.route');
const docsRoute = require("./docs.route");
const config = require("../config/config");
const facilityAccessLevelRoute = require("./facilityAccessLevel.route");
const masterDataRoute = require("./masterData.route");
const siteSettingRoute = require("./siteSetting.route");
const appointmentRoute = require("./appointment.route");
const patientGuestRoute = require("./patientGuest.route");
const patientRoute = require("./patient.route");
const identityRoute = require("./identity.route");
const profileRoute = require("./profile.route");
const languageRoute = require("./language.route");
const kioskRoute = require("./kiosk.route");
const delegateRoute = require("./delegates.route");
const trainingRoute = require("./training.route");
const vehicleRoute = require("./vehicle.route");
const documentRoute = require("./document.route");
const guestRoute = require("./guest.route");
const visitotHubRoute = require('./visitotHub.route');
const visitRoute = require('./visit.route');

const router = express.Router();

// routes available for every enviorment
const defaultRoutes = [
  { path: "/health", route: healthRoute },
  { path: "/auth", route: authRoute },
  { path: "/auth", route: authExternalRoute },
  
  { path: "/site-setting", route: siteSettingRoute },
  { path: "/master-data", route: masterDataRoute },

  { path: "/facility", route: facilityRoute },
  { path: "/facility/buildings/:facilityId", route: buildingRoute },
  { path: "/facility/floors/:facilityId", route: floorRoute },
  { path: "/facility/rooms/:facilityId", route: roomRoute },
  { path: "/facility/access-levels/:facilityId", route: facilityAccessLevelRoute },
  
  { path: "/system", route: systemRoute },
  { path: "/access-level", route: accessLevelRoute },

  { path: "/watchlist", route: watchlistRoute },

  { path: "/countries", route: countryRoute },
  { path: "/states", route: stateRoute },
  { path: "/timezones", route: timezoneRoute },

  { path: "/appointments", route: appointmentRoute },
  { path: "/appointments", route: patientGuestRoute },
  { path: "/patients", route: patientRoute },
  { path: "/identity",route:identityRoute},
  { path: "/profile", route: profileRoute },
  { path: "/languages", route: languageRoute },

  { path: "/kiosk", route: kioskRoute },
  { path: "/delegates", route: delegateRoute },
  { path: "/training", route: trainingRoute },
  { path: "/vehicles", route: vehicleRoute },
  { path: "/documents", route: documentRoute },
  { path: "/guests", route: guestRoute },
  { path: "/visitor-hub", route: visitotHubRoute },
  { path: "/visits", route: visitRoute },

];

// routes available only in development mode
const devRoutes = [
  {
    path: "/docs",
    route: docsRoute,
  },
];

defaultRoutes.forEach((route) => {
  router.use(route.path, route.route);
});

if (config.env === "development" || config.env === "local") {
  devRoutes.forEach((route) => {
    router.use(route.path, route.route);
  });
}

module.exports = router;
