const Joi = require("joi");

const setCache = {
  body: Joi.object().keys({
    operation: Joi.string()
      .valid("refresh", "remove")
      .default("refresh")
      .description("Operation to perform on the cache: 'refresh' or 'remove'"),
    keys: Joi.array()
      .items(
        Joi.string()
          .pattern(/^(eventConfig:.+|masterData:.+)$/)
          .description(
            "A cache key that must match 'eventConfig:*', or 'masterData:*'"
          )
      )
      .min(1)
      .required()
      .description(
        "An array of cache keys to operate on."
      ),
  }),
};

const getCache = {
  query: Joi.object().keys({
    keys: Joi.array()
      .items(
        Joi.string()
          .pattern(/^(eventConfig:.+|masterData:.+)$/)
          .description(
            "A cache key that must match 'eventConfig:*', or 'masterData:*'"
          )
      )
      .min(1)
      .single()
      .description(
        "An array of cache keys to retrieve."
      ),
  }),
};

module.exports = { setCache, getCache };
