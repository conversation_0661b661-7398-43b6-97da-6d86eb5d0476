const { MasterData } = require("../models");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const httpStatus = require("http-status");
const models = require("../models");
const cache = require("../config/caching");
const { getActiveLanguagesFromCache } = require("../helpers/caching.helper");
const { Op } = require("sequelize");
const { paginate } = require("../models/plugins/paginate.plugin");

/**
 * @desc    Retrieve key values of master data filtered by group(s)
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing master data key–value pairs
 */
exports.getMasterData = catchAsync(async (req, res) => {
  let { groups } = req.query;

  // Ensure groups is an array
  if (!Array.isArray(groups)) {
    groups = [groups];
  }
  groups = groups.map(String);

  // Object to store final results per group
  const result = {};
  // Array to hold groups that are not in cache
  const groupsToQuery = [];
  // Check cache for each individual group
  await Promise.all(
    groups.map(async (group) => {
      const cacheKey = `masterData:${group}`;
      const cachedData = await cache.get(cacheKey);
      if (cachedData) {
        result[group] = cachedData;
      } else {
        groupsToQuery.push(group);
      }
    })
  );

  // If any groups are missing in the cache, query the database for them
  if (groupsToQuery.length > 0) {
    const data = await MasterData.findAll({
      where: { group: groupsToQuery },
      attributes: ["group", "key", "value"],
    });

    // Organize the queried data by group
    const queriedResult = {};
    data.forEach((item) => {
      const grp = item.group;
      if (!queriedResult[grp]) {
        queriedResult[grp] = [];
      }
      queriedResult[grp].push({ key: item.key, value: item.value });
    });

    // Cache the results individually for each group and merge them into final result
    for (const grp of groupsToQuery) {
      const groupData = queriedResult[grp] || [];
      const cacheKey = `masterData:${grp}`;
      await cache.set(cacheKey, groupData, 3600000 * 1); // TTL (milliseconds x 1 = 1 hour)
      result[grp] = groupData;
    }
  }

  sendSuccess(res, "Master data retrieved successfully", httpStatus.OK, result);
});

/**
 * Get image for any model by model name and instance ID.
 *
 * The client must send:
 *  - In req.params: the model name (e.g. "Facility") and the instance id.
 *  - In req.query: an optional media key.
 *
 * The endpoint retrieves the instance using its primary key and includes the associated
 * media record (aliased as "imageMedia"). It then returns the media key
 * and its value.
 *
 * @async
 * @function getImage
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Request parameters.
 * @param {string} req.params.model - The model name from which the image is to be fetched.
 * @param {Object} req.query - Query parameters.
 * @param {string} [req.query.key] - The media key (e.g. "image").
 * @param {string} [req.query.value] - The media key (e.g. "3b18b1d4-e172-480e-9022-33a3df91e9ab" ).
 * @param {Object} res - Express response object.
 * @param {Function} next - Express next middleware.
 * @returns {Promise<void>} Returns the media record with its key and value.
 */
exports.getMedia = catchAsync(async (req, res, next) => {
  const { model } = req.params;
  const { key, value, thumbnail_only } = req.query;
  const associationAlias = key + "Media";

  // Convert model name to title case to match the models object keys
  const { titleCase } = require("../helpers/global.helper");
  const modelName = titleCase(model);

  // Check if the model exists
  if (!models[modelName]) {
    return sendError(
      res,
      `Model "${model}" not found`,
      httpStatus.BAD_REQUEST
    );
  }

  // Find the parent instance where the media field equals the given UUID, including the associated media record.
  const instance = await models[modelName].findOne({
    where: { [key]: value },
    include: [{ association: associationAlias }],
  });

  if (!instance) {
    return sendError(
      res,
      `${model} instance not found for the provided media reference`,
      httpStatus.NOT_FOUND
    );
  }

  const mediaRecord = instance[associationAlias];
  if (!mediaRecord) {
    return sendError(
      res,
      `No media found for key "${key}"`,
      httpStatus.NOT_FOUND
    );
  }

  // Convert thumbnail_only string to boolean (query params come as strings)
  const isThumbnailOnly = thumbnail_only === 'true' || thumbnail_only === true;

  // Return the media record with its key and value and pick either thumbnail or full image
  // If thumbnail is requested but doesn't exist, fallback to full image
  let output;
  let actualType;

  if (isThumbnailOnly) {
    if (mediaRecord.thumbnail) {
      output = mediaRecord.thumbnail;
      actualType = "thumbnail";
    } else if (mediaRecord.value) {
      output = mediaRecord.value;
      actualType = "image (thumbnail not available, showing full image)";
    } else {
      output = null;
      actualType = "thumbnail";
    }
  } else {
    output = mediaRecord.value;
    actualType = "image";
  }

  // Debug logging removed - fallback logic implemented

  if (!output) {
    return sendError(
      res,
      `${model} ${actualType} not available`,
      httpStatus.NOT_FOUND
    );
  }

  return sendSuccess(
    res,
    `${model} ${actualType} retrieved successfully`,
    httpStatus.OK,
    { key: mediaRecord.key, value: output }
  );
});

/**
 * @desc    Retrieve all countries
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing all countries
 */
exports.getAllCountries = catchAsync(async (req, res) => {
  const countries = await models.Country.findAll({ attributes: ["country_id", "name"] });
  sendSuccess(res, "Countries retrieved successfully", httpStatus.OK, countries);
});

/**
 * @desc    Retrieve all states
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing all states
 */
exports.getAllStates = catchAsync(async (req, res) => {
  const states = await models.State.findAll({ attributes: ["state_id", "name"] });
  sendSuccess(res, "States retrieved successfully", httpStatus.OK, states);
});

/**
 * @desc    Retrieve all states based on country ID
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing states filtered by country ID
 */
exports.getStatesByCountry = catchAsync(async (req, res) => {
  const { countryId } = req.params;

  const states = await models.State.findAll({
    where: { country_id: countryId },
    attributes: ["state_id", "name"],
  });
  sendSuccess(res, "States retrieved successfully", httpStatus.OK, states);
});

/**
 * @desc    Retrieve all accessLevelName that are referenced in FacilityAccessLevel with optional filters
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing filtered accesslevelname
 */
exports.getAllAccessLevel = catchAsync(async (req, res) => {
  const {
    default_access_guest,
    requestable_guest,
    facility_id,
  } = req.query;

  const facilityAccessWhere = {};

  if (default_access_guest !== undefined) {
    facilityAccessWhere.default_access_guest = default_access_guest;
  }

  if (requestable_guest !== undefined) {
    facilityAccessWhere.requestable_guest = requestable_guest;
  }

  const accessLevels = await models.AccessLevel.findAll({
    include: [
      {
        model: models.FacilityAccessLevel,
        as: "facility_access_level",
        ...(Object.keys(facilityAccessWhere).length > 0 && {
          where: facilityAccessWhere,
        }),
        attributes: [],
        required: true,
      },
    ],
    ...(facility_id && {
      where: {
        [Op.or]: [
          { facility_id },
          { "$facility_access_level.facility_id$": facility_id },
        ],
      },
    }),
    attributes: ["access_level_id", "name"],
    group: ["AccessLevel.access_level_id", "AccessLevel.name"],
  });

  sendSuccess(res, "AccessLevel retrieved successfully", httpStatus.OK, accessLevels);
});



exports.getIdentityHub = catchAsync(async (req, res) => {
  const { search } = req.query;
  const where = {};
  if (search) {
    where[Op.or] = [
      { first_name: { [Op.iLike]: `%${search}%` } },
      { last_name: { [Op.iLike]: `%${search}%` } },
      { eid: { [Op.iLike]: `%${search}%` } },
    ];
  }
  const identities = await paginate(
    models.Identity,
    { where },
    {
      attributes: [
        [models.sequelize.literal("CONCAT(first_name, ' ', last_name)"), "name"],
        "eid",
      ],
    }
  );
  // Filter the result to only include name and eid in the data array
  if (identities && Array.isArray(identities.data)) {
    identities.data = identities.data.map(item => ({
      id: item.identity_id,
      name: item.first_name + " " + item.last_name,
      eid: item.eid
    }));
  }
  sendSuccess(
    res,
    "Identity retrieved successfully",
    httpStatus.OK,
    identities
  );
});
/**
 * @desc    Retrieve all 
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing all timezones
 */
exports.getAllTimezones = catchAsync(async (req, res) => {
  const timezones = await models.Timezone.findAll({ attributes: ["timezone_id", "code"] });
  sendSuccess(res, "Timezones retrieved successfully", httpStatus.OK, timezones);
});

/**
 * @desc    Retrieve all active languages
 * @param   {Object} req - Express request object
 * @param   {Object} res - Express response object
 * @returns {JSON} JSON object containing all active languages
 */
exports.getLanguages = catchAsync(async (req, res) => {
  const activeLanguages = await getActiveLanguagesFromCache();
  sendSuccess(res, "Active languages retrieved successfully", httpStatus.OK, activeLanguages);
});


